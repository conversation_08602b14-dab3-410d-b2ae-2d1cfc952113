using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using DLT_CP.Services;
using DLT_CP.Models;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using DLT_CP.Exceptions;

namespace DLT_CP.Tests.Services;

[TestClass]
public class FrequencyAnalyzerTests
{
    private Mock<ILogger<FrequencyAnalyzer>> _mockLogger;
    private Mock<GlobalExceptionHandler> _mockExceptionHandler;
    private FrequencyAnalyzer _frequencyAnalyzer;
    private List<LotteryDataRow> _testData;
    
    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<FrequencyAnalyzer>>();
        _mockExceptionHandler = new Mock<GlobalExceptionHandler>();
        
        _frequencyAnalyzer = new FrequencyAnalyzer(_mockLogger.Object, _mockExceptionHandler.Object);
        
        // 创建测试数据
        _testData = new List<LotteryDataRow>
        {
            new LotteryDataRow
            {
                期号 = "23001",
                开奖日期 = new DateTime(2023, 1, 1),
                红球 = new[] { 1, 5, 12, 18, 25 },
                蓝球 = new[] { 3, 8 }
            },
            new LotteryDataRow
            {
                期号 = "23002",
                开奖日期 = new DateTime(2023, 1, 3),
                红球 = new[] { 2, 7, 15, 22, 28 },
                蓝球 = new[] { 5, 11 }
            },
            new LotteryDataRow
            {
                期号 = "23003",
                开奖日期 = new DateTime(2023, 1, 5),
                红球 = new[] { 1, 8, 16, 23, 30 },
                蓝球 = new[] { 3, 9 }
            },
            new LotteryDataRow
            {
                期号 = "23004",
                开奖日期 = new DateTime(2023, 1, 7),
                红球 = new[] { 3, 9, 17, 24, 31 },
                蓝球 = new[] { 1, 7 }
            },
            new LotteryDataRow
            {
                期号 = "23005",
                开奖日期 = new DateTime(2023, 1, 9),
                红球 = new[] { 1, 10, 18, 25, 32 },
                蓝球 = new[] { 2, 8 }
            }
        };
    }
    
    [TestCleanup]
    public void Cleanup()
    {
        _frequencyAnalyzer?.Dispose();
    }
    
    [TestMethod]
    public async Task TrainAsync_WithValidData_ShouldCompleteSuccessfully()
    {
        // Act
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        
        // Assert
        Assert.IsTrue(_frequencyAnalyzer.IsTrained);
        
        // 验证日志记录了训练完成
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("频率分析训练完成")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }
    
    [TestMethod]
    public async Task TrainAsync_WithEmptyData_ShouldThrowException()
    {
        // Arrange
        var emptyData = new List<LotteryDataRow>();
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<ArgumentException>(
            () => _frequencyAnalyzer.TrainAsync(emptyData, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task TrainAsync_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        await Assert.ThrowsExceptionAsync<ArgumentNullException>(
            () => _frequencyAnalyzer.TrainAsync(null, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task PredictAsync_WithoutTraining_ShouldThrowException()
    {
        // Act & Assert
        await Assert.ThrowsExceptionAsync<InvalidOperationException>(
            () => _frequencyAnalyzer.PredictAsync(_testData.Take(3).ToList(), CancellationToken.None));
    }
    
    [TestMethod]
    public async Task PredictAsync_AfterTraining_ShouldReturnValidPrediction()
    {
        // Arrange
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        var recentData = _testData.Take(3).ToList();
        
        // Act
        var result = await _frequencyAnalyzer.PredictAsync(recentData, CancellationToken.None);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.IsNotNull(result.期号);
        Assert.IsTrue(result.预测时间 <= DateTime.Now);
        Assert.IsNotNull(result.红球预测);
        Assert.IsNotNull(result.蓝球预测);
        Assert.AreEqual(5, result.红球预测.Length);
        Assert.AreEqual(2, result.蓝球预测.Length);
        Assert.IsTrue(result.预测置信度 >= 0 && result.预测置信度 <= 1);
        
        // 验证红球范围和唯一性
        Assert.IsTrue(result.红球预测.All(x => x >= 1 && x <= 35));
        Assert.AreEqual(5, result.红球预测.Distinct().Count());
        
        // 验证蓝球范围和唯一性
        Assert.IsTrue(result.蓝球预测.All(x => x >= 1 && x <= 12));
        Assert.AreEqual(2, result.蓝球预测.Distinct().Count());
    }
    
    [TestMethod]
    public async Task PredictBatchAsync_AfterTraining_ShouldReturnValidPredictions()
    {
        // Arrange
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        var batchData = new List<List<LotteryDataRow>>
        {
            _testData.Take(2).ToList(),
            _testData.Take(3).ToList(),
            _testData.Take(4).ToList()
        };
        
        // Act
        var results = await _frequencyAnalyzer.PredictBatchAsync(batchData, CancellationToken.None);
        
        // Assert
        Assert.IsNotNull(results);
        Assert.AreEqual(3, results.Count);
        
        foreach (var result in results)
        {
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.期号);
            Assert.AreEqual(5, result.红球预测.Length);
            Assert.AreEqual(2, result.蓝球预测.Length);
            Assert.IsTrue(result.预测置信度 >= 0 && result.预测置信度 <= 1);
        }
    }
    
    [TestMethod]
    public async Task PredictAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        var recentData = _testData.Take(3).ToList();
        
        var cts = new CancellationTokenSource();
        cts.Cancel(); // 立即取消
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<OperationCanceledException>(
            () => _frequencyAnalyzer.PredictAsync(recentData, cts.Token));
    }
    
    [TestMethod]
    public async Task TrainAsync_WithDuplicatePeriods_ShouldHandleGracefully()
    {
        // Arrange
        var dataWithDuplicates = new List<LotteryDataRow>(_testData)
        {
            // 添加重复期号的数据
            new LotteryDataRow
            {
                期号 = "23001", // 重复期号
                开奖日期 = new DateTime(2023, 1, 11),
                红球 = new[] { 4, 11, 19, 26, 33 },
                蓝球 = new[] { 6, 10 }
            }
        };
        
        // Act
        await _frequencyAnalyzer.TrainAsync(dataWithDuplicates, CancellationToken.None);
        
        // Assert
        Assert.IsTrue(_frequencyAnalyzer.IsTrained);
        
        // 验证警告日志
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("重复期号")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }
    
    [TestMethod]
    public async Task PredictAsync_ShouldGenerateConsistentResults()
    {
        // Arrange
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        var recentData = _testData.Take(3).ToList();
        
        // Act - 多次预测
        var results = new List<PredictionResult>();
        for (int i = 0; i < 5; i++)
        {
            var result = await _frequencyAnalyzer.PredictAsync(recentData, CancellationToken.None);
            results.Add(result);
        }
        
        // Assert - 验证结果的一致性（基于相同输入应该有相似的置信度）
        var confidences = results.Select(r => r.预测置信度).ToList();
        var avgConfidence = confidences.Average();
        var maxDeviation = confidences.Max(c => Math.Abs(c - avgConfidence));
        
        Assert.IsTrue(maxDeviation < 0.1, $"预测置信度变化过大: {maxDeviation}");
    }
    
    [TestMethod]
    public async Task TrainAsync_WithLargeDataset_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var largeDataset = new List<LotteryDataRow>();
        var baseDate = new DateTime(2020, 1, 1);
        
        // 生成1000条测试数据
        for (int i = 1; i <= 1000; i++)
        {
            largeDataset.Add(new LotteryDataRow
            {
                期号 = $"20{i:D3}",
                开奖日期 = baseDate.AddDays(i * 2),
                红球 = GenerateRandomRedBalls(),
                蓝球 = GenerateRandomBlueBalls()
            });
        }
        
        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        await _frequencyAnalyzer.TrainAsync(largeDataset, CancellationToken.None);
        stopwatch.Stop();
        
        // Assert
        Assert.IsTrue(_frequencyAnalyzer.IsTrained);
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 10000, $"训练时间过长: {stopwatch.ElapsedMilliseconds}ms");
    }
    
    [TestMethod]
    public void Name_ShouldReturnCorrectValue()
    {
        // Act & Assert
        Assert.AreEqual("频率分析预测器", _frequencyAnalyzer.Name);
    }
    
    [TestMethod]
    public void Description_ShouldReturnCorrectValue()
    {
        // Act & Assert
        Assert.IsTrue(_frequencyAnalyzer.Description.Contains("历史号码出现频率"));
    }
    
    [TestMethod]
    public void IsTrained_InitiallyFalse_AfterTrainingTrue()
    {
        // Assert - 初始状态
        Assert.IsFalse(_frequencyAnalyzer.IsTrained);
    }
    
    [TestMethod]
    public async Task GetModelInfo_AfterTraining_ShouldReturnValidInfo()
    {
        // Arrange
        await _frequencyAnalyzer.TrainAsync(_testData, CancellationToken.None);
        
        // Act
        var modelInfo = _frequencyAnalyzer.GetModelInfo();
        
        // Assert
        Assert.IsNotNull(modelInfo);
        Assert.IsTrue(modelInfo.Contains("训练数据量"));
        Assert.IsTrue(modelInfo.Contains(_testData.Count.ToString()));
    }
    
    [TestMethod]
    public void Dispose_ShouldCleanupResources()
    {
        // Act
        _frequencyAnalyzer.Dispose();
        
        // Assert - 验证日志记录了释放操作
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("频率分析预测器已释放")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }
    
    private int[] GenerateRandomRedBalls()
    {
        var random = new Random();
        var balls = new HashSet<int>();
        
        while (balls.Count < 5)
        {
            balls.Add(random.Next(1, 36));
        }
        
        return balls.OrderBy(x => x).ToArray();
    }
    
    private int[] GenerateRandomBlueBalls()
    {
        var random = new Random();
        var balls = new HashSet<int>();
        
        while (balls.Count < 2)
        {
            balls.Add(random.Next(1, 13));
        }
        
        return balls.OrderBy(x => x).ToArray();
    }
}