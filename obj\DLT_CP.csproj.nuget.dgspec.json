{"format": 1, "restore": {"E:\\桌面\\DLP_CP\\DLT_CP\\DLT_CP.csproj": {}}, "projects": {"E:\\桌面\\DLP_CP\\DLT_CP\\DLT_CP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\桌面\\DLP_CP\\DLT_CP\\DLT_CP.csproj", "projectName": "DLT_CP", "projectPath": "E:\\桌面\\DLP_CP\\DLT_CP\\DLT_CP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\桌面\\DLP_CP\\DLT_CP\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "MSTest.TestAdapter": {"target": "Package", "version": "[3.1.1, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.DataView": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.ML.FastTree": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "TorchSharp": {"target": "Package", "version": "[0.102.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}