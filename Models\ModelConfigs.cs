using System.ComponentModel.DataAnnotations;

namespace DLT_CP.Models;

/// <summary>
/// 模型配置
/// </summary>
public class ModelConfigs
{
    /// <summary>
    /// 马尔可夫链配置
    /// </summary>
    public MarkovConfig MarkovChain { get; set; } = new();
    
    /// <summary>
    /// 神经网络配置
    /// </summary>
    public NeuralNetworkConfig NeuralNetwork { get; set; } = new();
}

/// <summary>
/// 马尔可夫链配置
/// </summary>
public class MarkovConfig
{
    /// <summary>
    /// 马尔可夫阶数
    /// </summary>
    [Range(1, 10)]
    public int Order { get; set; } = 2;
}

/// <summary>
/// 神经网络配置
/// </summary>
public class NeuralNetworkConfig
{
    /// <summary>
    /// 训练轮数
    /// </summary>
    [Range(1, 10000)]
    public int Epochs { get; set; } = 100;
    
    /// <summary>
    /// 批次大小
    /// </summary>
    [Range(1, 1000)]
    public int BatchSize { get; set; } = 32;
    
    /// <summary>
    /// 学习率
    /// </summary>
    [Range(0.0001, 1.0)]
    public double LearningRate { get; set; } = 0.001;
    
    /// <summary>
    /// 隐藏层大小
    /// </summary>
    [Range(10, 1000)]
    public int HiddenSize { get; set; } = 128;
}