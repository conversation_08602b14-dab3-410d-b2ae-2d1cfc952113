using System.Windows.Controls;
using DLT_CP.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace DLT_CP.Views
{
    /// <summary>
    /// PerformanceMonitorView.xaml 的交互逻辑
    /// </summary>
    public partial class PerformanceMonitorView : UserControl
    {
        public PerformanceMonitorView()
        {
            InitializeComponent();
            
            // 从依赖注入容器获取 ViewModel
            if (Application.Current is App app)
            {
                DataContext = app.ServiceProvider.GetRequiredService<PerformanceMonitorViewModel>();
            }
        }
    }
}