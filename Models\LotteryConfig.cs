using System.ComponentModel.DataAnnotations;

namespace DLT_CP.Models;

/// <summary>
/// 彩票配置
/// </summary>
public class LotteryConfig
{
    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    [Required]
    public string ConnectionString { get; set; } = string.Empty;
    
    /// <summary>
    /// 彩票数据文件路径
    /// </summary>
    [Required]
    public string DataPath { get; set; } = string.Empty;
    
    /// <summary>
    /// 红球数量
    /// </summary>
    [Range(1, 50)]
    public int RedBallCount { get; set; } = 35;
    
    /// <summary>
    /// 蓝球数量
    /// </summary>
    [Range(1, 20)]
    public int BlueBallCount { get; set; } = 12;
    
    /// <summary>
    /// 每期选择的红球数量
    /// </summary>
    [Range(1, 10)]
    public int RedBallSelection { get; set; } = 5;
    
    /// <summary>
    /// 每期选择的蓝球数量
    /// </summary>
    [Range(1, 5)]
    public int BlueBallSelection { get; set; } = 2;
}