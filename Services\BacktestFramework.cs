using DLT_CP.Models;
using Microsoft.Extensions.Logging;

namespace DLT_CP.Services;

/// <summary>
/// 回测框架实现
/// </summary>
public class BacktestFramework : IBacktestFramework
{
    private readonly ILogger<BacktestFramework> _logger;
    private readonly IDataService _dataService;
    
    public BacktestFramework(ILogger<BacktestFramework> logger, IDataService dataService)
    {
        _logger = logger;
        _dataService = dataService;
    }
    
    public async Task<BacktestResult> RunBacktestAsync(
        IPredictor predictor, 
        IEnumerable<LotteryDataRow> historicalData, 
        int trainPeriods, 
        int testPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始回测 - 预测器: {PredictorName}, 训练期数: {TrainPeriods}, 测试期数: {TestPeriods}", 
                predictor.Name, trainPeriods, testPeriods);
            
            var dataList = historicalData.OrderBy(x => x.DrawDate).ToList();
            
            if (dataList.Count < trainPeriods + testPeriods)
            {
                throw new ArgumentException($"历史数据不足，需要至少 {trainPeriods + testPeriods} 期数据，实际只有 {dataList.Count} 期");
            }
            
            var result = new BacktestResult
            {
                ModelName = predictor.Name,
                BacktestTime = DateTime.Now,
                TotalPeriods = testPeriods
            };
            
            // 准备训练数据
            var trainData = dataList.Take(trainPeriods).ToList();
            
            // 训练模型
            progress?.Report(new BacktestProgress { CurrentPeriod = 0, TotalPeriods = testPeriods, Message = "正在训练模型..." });
            await predictor.TrainAsync(trainData, cancellationToken);
            
            // 执行回测
            for (int i = 0; i < testPeriods; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                var testIndex = trainPeriods + i;
                var actualData = dataList[testIndex];
                
                // 预测
                var prediction = await predictor.PredictAsync(actualData.PeriodNumber, cancellationToken);
                
                // 计算命中情况
                var redHits = prediction.RedBalls.Intersect(actualData.RedBalls).Count();
                var blueHits = prediction.BlueBalls.Intersect(actualData.BlueBalls).Count();
                
                // 更新统计
                result.RedBallHits[redHits]++;
                result.BlueBallHits[blueHits]++;
                
                // 记录最佳命中
                var totalHits = redHits + blueHits;
                if (totalHits > result.BestRedHits + result.BestBlueHits)
                {
                    result.BestRedHits = redHits;
                    result.BestBlueHits = blueHits;
                    result.BestHitPeriod = actualData.PeriodNumber;
                }
                
                // 报告进度
                progress?.Report(new BacktestProgress 
                { 
                    CurrentPeriod = i + 1, 
                    TotalPeriods = testPeriods, 
                    Message = $"测试期号: {actualData.PeriodNumber}, 红球命中: {redHits}, 蓝球命中: {blueHits}"
                });
                
                _logger.LogDebug("期号 {Period}: 红球命中 {RedHits}/5, 蓝球命中 {BlueHits}/2", 
                    actualData.PeriodNumber, redHits, blueHits);
            }
            
            // 计算最终统计
            CalculateFinalStatistics(result);
            
            _logger.LogInformation("回测完成 - 平均红球命中: {AvgRed:F2}, 平均蓝球命中: {AvgBlue:F2}, 综合评分: {Score:F2}", 
                result.AverageRedHits, result.AverageBlueHits, result.OverallScore);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "回测过程中发生错误");
            throw;
        }
    }
    
    public async Task<CrossValidationResult> RunCrossValidationAsync(
        IPredictor predictor, 
        IEnumerable<LotteryDataRow> data, 
        int folds, 
        int trainPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始交叉验证 - 预测器: {PredictorName}, 折数: {Folds}, 训练期数: {TrainPeriods}", 
                predictor.Name, folds, trainPeriods);
            
            var dataList = data.OrderBy(x => x.DrawDate).ToList();
            var totalPeriods = dataList.Count;
            var foldSize = totalPeriods / folds;
            
            if (foldSize < trainPeriods + 10) // 至少需要10期测试数据
            {
                throw new ArgumentException($"数据量不足以进行 {folds} 折交叉验证");
            }
            
            var result = new CrossValidationResult
            {
                ModelName = predictor.Name,
                Folds = folds,
                TrainPeriods = trainPeriods
            };
            
            for (int fold = 0; fold < folds; fold++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                // 准备当前折的数据
                var testStartIndex = fold * foldSize;
                var testEndIndex = Math.Min(testStartIndex + foldSize, totalPeriods);
                
                var trainData = dataList.Take(testStartIndex)
                    .Concat(dataList.Skip(testEndIndex))
                    .Take(trainPeriods)
                    .ToList();
                
                var testData = dataList.Skip(testStartIndex).Take(testEndIndex - testStartIndex).ToList();
                
                if (trainData.Count < trainPeriods || testData.Count < 5)
                {
                    _logger.LogWarning("第 {Fold} 折数据不足，跳过", fold + 1);
                    continue;
                }
                
                // 执行回测
                var foldResult = await RunBacktestAsync(predictor, trainData.Concat(testData), 
                    trainData.Count, testData.Count, null, cancellationToken);
                
                result.FoldResults.Add(foldResult);
                
                progress?.Report(new BacktestProgress 
                { 
                    CurrentPeriod = fold + 1, 
                    TotalPeriods = folds, 
                    Message = $"完成第 {fold + 1}/{folds} 折验证"
                });
            }
            
            // 计算平均结果
            if (result.FoldResults.Any())
            {
                result.AverageScore = result.FoldResults.Average(x => x.OverallScore);
                result.ScoreStandardDeviation = CalculateStandardDeviation(result.FoldResults.Select(x => x.OverallScore));
                result.AverageRedHits = result.FoldResults.Average(x => x.AverageRedHits);
                result.AverageBlueHits = result.FoldResults.Average(x => x.AverageBlueHits);
            }
            
            _logger.LogInformation("交叉验证完成 - 平均评分: {AvgScore:F2} ± {StdDev:F2}", 
                result.AverageScore, result.ScoreStandardDeviation);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "交叉验证过程中发生错误");
            throw;
        }
    }
    
    public async Task<RollingWindowResult> RunRollingWindowBacktestAsync(
        IPredictor predictor, 
        IEnumerable<LotteryDataRow> data, 
        int windowSize, 
        int stepSize,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始滚动窗口回测 - 预测器: {PredictorName}, 窗口大小: {WindowSize}, 步长: {StepSize}", 
                predictor.Name, windowSize, stepSize);
            
            var dataList = data.OrderBy(x => x.DrawDate).ToList();
            var totalPeriods = dataList.Count;
            
            if (totalPeriods < windowSize + stepSize)
            {
                throw new ArgumentException($"数据量不足以进行滚动窗口回测，需要至少 {windowSize + stepSize} 期数据");
            }
            
            var result = new RollingWindowResult
            {
                ModelName = predictor.Name,
                WindowSize = windowSize,
                StepSize = stepSize
            };
            
            var windowCount = (totalPeriods - windowSize) / stepSize + 1;
            
            for (int window = 0; window < windowCount; window++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                var startIndex = window * stepSize;
                var endIndex = startIndex + windowSize;
                
                if (endIndex >= totalPeriods)
                    break;
                
                var windowData = dataList.Skip(startIndex).Take(windowSize).ToList();
                var trainSize = (int)(windowSize * 0.8); // 80% 用于训练
                var testSize = windowSize - trainSize;
                
                var windowResult = await RunBacktestAsync(predictor, windowData, 
                    trainSize, testSize, null, cancellationToken);
                
                result.WindowResults.Add(windowResult);
                
                progress?.Report(new BacktestProgress 
                { 
                    CurrentPeriod = window + 1, 
                    TotalPeriods = windowCount, 
                    Message = $"完成第 {window + 1}/{windowCount} 个窗口"
                });
            }
            
            // 计算趋势分析
            if (result.WindowResults.Count > 1)
            {
                var scores = result.WindowResults.Select(x => x.OverallScore).ToList();
                result.PerformanceTrend = CalculateTrend(scores);
                result.AverageScore = scores.Average();
                result.ScoreStandardDeviation = CalculateStandardDeviation(scores);
            }
            
            _logger.LogInformation("滚动窗口回测完成 - 窗口数: {WindowCount}, 平均评分: {AvgScore:F2}, 趋势: {Trend}", 
                result.WindowResults.Count, result.AverageScore, result.PerformanceTrend);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "滚动窗口回测过程中发生错误");
            throw;
        }
    }
    
    public async Task<ModelComparisonResult> CompareModelsAsync(
        IEnumerable<IPredictor> predictors, 
        IEnumerable<LotteryDataRow> data, 
        int trainPeriods, 
        int testPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var predictorList = predictors.ToList();
            _logger.LogInformation("开始模型比较 - 模型数量: {ModelCount}, 训练期数: {TrainPeriods}, 测试期数: {TestPeriods}", 
                predictorList.Count, trainPeriods, testPeriods);
            
            var result = new ModelComparisonResult
            {
                TrainPeriods = trainPeriods,
                TestPeriods = testPeriods,
                ComparisonTime = DateTime.Now
            };
            
            var totalSteps = predictorList.Count;
            
            for (int i = 0; i < predictorList.Count; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                
                var predictor = predictorList[i];
                
                try
                {
                    var modelResult = await RunBacktestAsync(predictor, data, trainPeriods, testPeriods, null, cancellationToken);
                    result.ModelResults[predictor.Name] = modelResult;
                    
                    progress?.Report(new BacktestProgress 
                    { 
                        CurrentPeriod = i + 1, 
                        TotalPeriods = totalSteps, 
                        Message = $"完成模型 {predictor.Name} 的回测"
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "模型 {ModelName} 回测失败", predictor.Name);
                    // 继续处理其他模型
                }
            }
            
            // 排序和分析
            if (result.ModelResults.Any())
            {
                result.BestModel = result.ModelResults
                    .OrderByDescending(x => x.Value.OverallScore)
                    .First().Key;
                
                result.WorstModel = result.ModelResults
                    .OrderBy(x => x.Value.OverallScore)
                    .First().Key;
                
                // 计算统计显著性（简化版）
                var scores = result.ModelResults.Values.Select(x => x.OverallScore).ToList();
                result.ScoreRange = scores.Max() - scores.Min();
                result.AverageScore = scores.Average();
            }
            
            _logger.LogInformation("模型比较完成 - 最佳模型: {BestModel}, 最差模型: {WorstModel}, 评分范围: {ScoreRange:F2}", 
                result.BestModel, result.WorstModel, result.ScoreRange);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模型比较过程中发生错误");
            throw;
        }
    }
    
    private void CalculateFinalStatistics(BacktestResult result)
    {
        // 计算平均命中数
        result.AverageRedHits = result.RedBallHits.Sum(kvp => kvp.Key * kvp.Value) / (double)result.TotalPeriods;
        result.AverageBlueHits = result.BlueBallHits.Sum(kvp => kvp.Key * kvp.Value) / (double)result.TotalPeriods;
        
        // 计算命中率
        result.RedHitRate = result.AverageRedHits / 5.0;
        result.BlueHitRate = result.AverageBlueHits / 2.0;
        
        // 计算综合评分（可以根据需要调整权重）
        var redScore = result.AverageRedHits * 20; // 红球每命中一个得20分
        var blueScore = result.AverageBlueHits * 30; // 蓝球每命中一个得30分
        
        // 奖级加分
        var bonusScore = 0.0;
        if (result.RedBallHits.ContainsKey(5) && result.BlueBallHits.ContainsKey(2))
        {
            bonusScore += (result.RedBallHits[5] + result.BlueBallHits[2]) * 100; // 全中奖级加分
        }
        
        result.OverallScore = redScore + blueScore + bonusScore;
        
        // 确定准确度等级
        result.AccuracyLevel = result.OverallScore switch
        {
            >= 80 => "优秀",
            >= 60 => "良好",
            >= 40 => "一般",
            >= 20 => "较差",
            _ => "很差"
        };
    }
    
    private double CalculateStandardDeviation(IEnumerable<double> values)
    {
        var valueList = values.ToList();
        if (valueList.Count <= 1) return 0;
        
        var mean = valueList.Average();
        var sumOfSquares = valueList.Sum(x => Math.Pow(x - mean, 2));
        return Math.Sqrt(sumOfSquares / (valueList.Count - 1));
    }
    
    private string CalculateTrend(List<double> scores)
    {
        if (scores.Count < 2) return "无法确定";
        
        var firstHalf = scores.Take(scores.Count / 2).Average();
        var secondHalf = scores.Skip(scores.Count / 2).Average();
        
        var difference = secondHalf - firstHalf;
        
        return difference switch
        {
            > 5 => "显著上升",
            > 1 => "轻微上升",
            < -5 => "显著下降",
            < -1 => "轻微下降",
            _ => "基本稳定"
        };
    }
}