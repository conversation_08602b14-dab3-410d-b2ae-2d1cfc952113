using DLT_CP.Models;

namespace DLT_CP.Services;

/// <summary>
/// 预测器接口
/// </summary>
public interface IPredictor
{
    /// <summary>
    /// 预测器名称
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 预测器描述
    /// </summary>
    string Description { get; }
    
    /// <summary>
    /// 是否已训练
    /// </summary>
    bool IsTrained { get; }
    
    /// <summary>
    /// 训练模型
    /// </summary>
    /// <param name="data">训练数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>训练是否成功</returns>
    Task<bool> TrainAsync(IEnumerable<LotteryDataRow> data, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 预测下一期号码
    /// </summary>
    /// <param name="historyData">历史数据</param>
    /// <param name="nextPeriod">下一期期号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预测结果</returns>
    Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> historyData, string nextPeriod, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量预测
    /// </summary>
    /// <param name="historyData">历史数据</param>
    /// <param name="periods">要预测的期号列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预测结果列表</returns>
    Task<IEnumerable<PredictionResult>> PredictBatchAsync(IEnumerable<LotteryDataRow> historyData, IEnumerable<string> periods, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取模型参数
    /// </summary>
    /// <returns>模型参数字典</returns>
    Dictionary<string, object> GetModelParameters();
    
    /// <summary>
    /// 设置模型参数
    /// </summary>
    /// <param name="parameters">模型参数</param>
    void SetModelParameters(Dictionary<string, object> parameters);
    
    /// <summary>
    /// 保存模型
    /// </summary>
    /// <param name="filePath">保存路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SaveModelAsync(string filePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 加载模型
    /// </summary>
    /// <param name="filePath">模型文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task LoadModelAsync(string filePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取模型评估指标
    /// </summary>
    /// <returns>评估指标</returns>
    Dictionary<string, double> GetEvaluationMetrics();
}