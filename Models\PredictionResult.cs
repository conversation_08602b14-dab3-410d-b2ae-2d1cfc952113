namespace DLT_CP.Models;

/// <summary>
/// 预测结果模型
/// </summary>
public class PredictionResult
{
    /// <summary>
    /// 预测期号
    /// </summary>
    public string PeriodNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 预测时间
    /// </summary>
    public DateTime PredictionTime { get; set; }
    
    /// <summary>
    /// 预测的红球
    /// </summary>
    public int[] RedBalls { get; set; } = Array.Empty<int>();
    
    /// <summary>
    /// 预测的蓝球
    /// </summary>
    public int[] BlueBalls { get; set; } = Array.Empty<int>();
    
    /// <summary>
    /// 预测置信度 (0-1)
    /// </summary>
    public double Confidence { get; set; }
    
    /// <summary>
    /// 使用的预测模型
    /// </summary>
    public string ModelName { get; set; } = string.Empty;
    
    /// <summary>
    /// 预测依据的历史数据期数
    /// </summary>
    public int HistoryPeriods { get; set; }
    
    /// <summary>
    /// 模型评分
    /// </summary>
    public double ModelScore { get; set; }
    
    /// <summary>
    /// 预测备注
    /// </summary>
    public string? Notes { get; set; }
    
    /// <summary>
    /// 获取格式化的红球字符串
    /// </summary>
    public string RedBallsString => string.Join(", ", RedBalls.OrderBy(x => x));
    
    /// <summary>
    /// 获取格式化的蓝球字符串
    /// </summary>
    public string BlueBallsString => string.Join(", ", BlueBalls.OrderBy(x => x));
    
    /// <summary>
    /// 获取完整的预测号码字符串
    /// </summary>
    public string FullNumberString => $"{RedBallsString} + {BlueBallsString}";
    
    /// <summary>
    /// 验证预测结果是否有效
    /// </summary>
    public bool IsValid()
    {
        // 检查红球
        if (RedBalls.Length != 5)
            return false;
            
        if (RedBalls.Any(x => x < 1 || x > 35))
            return false;
            
        if (RedBalls.Distinct().Count() != 5)
            return false;
            
        // 检查蓝球
        if (BlueBalls.Length != 2)
            return false;
            
        if (BlueBalls.Any(x => x < 1 || x > 12))
            return false;
            
        if (BlueBalls.Distinct().Count() != 2)
            return false;
            
        return true;
    }
    
    /// <summary>
    /// 计算与实际开奖结果的匹配度
    /// </summary>
    public (int RedMatches, int BlueMatches) CalculateMatches(LotteryDataRow actual)
    {
        var redMatches = RedBalls.Intersect(actual.RedBalls).Count();
        var blueMatches = BlueBalls.Intersect(actual.BlueBalls).Count();
        return (redMatches, blueMatches);
    }
}