using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using DLT_CP.Services;
using DLT_CP.Models;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.IO;
using Microsoft.Extensions.Logging;
using DLT_CP.Exceptions;

namespace DLT_CP.Tests.Services;

[TestClass]
public class DataServiceTests
{
    private Mock<ILogger<DataService>> _mockLogger;
    private Mock<GlobalExceptionHandler> _mockExceptionHandler;
    private LotteryConfig _testConfig;
    private DataService _dataService;
    private string _testDataDirectory;
    
    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<DataService>>();
        _mockExceptionHandler = new Mock<GlobalExceptionHandler>();
        
        _testDataDirectory = Path.Combine(Path.GetTempPath(), "DLT_CP_Tests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDataDirectory);
        
        _testConfig = new LotteryConfig
        {
            DataPath = _testDataDirectory,
            SequenceLength = 10,
            BacktestPeriods = 50
        };
        
        _dataService = new DataService(_mockLogger.Object, _testConfig, _mockExceptionHandler.Object);
    }
    
    [TestCleanup]
    public void Cleanup()
    {
        _dataService?.Dispose();
        
        if (Directory.Exists(_testDataDirectory))
        {
            Directory.Delete(_testDataDirectory, true);
        }
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithValidCsvFile_ShouldReturnData()
    {
        // Arrange
        var csvContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                        "23001,2023-01-01,01,05,12,18,25,03,08\n" +
                        "23002,2023-01-03,02,07,15,22,28,05,11";
        
        var csvFilePath = Path.Combine(_testDataDirectory, "test_data.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        // Act
        var result = await _dataService.LoadDataAsync(csvFilePath, CancellationToken.None);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(2, result.Count);
        
        var firstRow = result[0];
        Assert.AreEqual("23001", firstRow.期号);
        Assert.AreEqual(new DateTime(2023, 1, 1), firstRow.开奖日期);
        CollectionAssert.AreEqual(new[] { 1, 5, 12, 18, 25 }, firstRow.红球);
        CollectionAssert.AreEqual(new[] { 3, 8 }, firstRow.蓝球);
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithValidJsonFile_ShouldReturnData()
    {
        // Arrange
        var testData = new List<LotteryDataRow>
        {
            new LotteryDataRow
            {
                期号 = "23001",
                开奖日期 = new DateTime(2023, 1, 1),
                红球 = new[] { 1, 5, 12, 18, 25 },
                蓝球 = new[] { 3, 8 }
            },
            new LotteryDataRow
            {
                期号 = "23002",
                开奖日期 = new DateTime(2023, 1, 3),
                红球 = new[] { 2, 7, 15, 22, 28 },
                蓝球 = new[] { 5, 11 }
            }
        };
        
        var jsonContent = JsonSerializer.Serialize(testData, new JsonSerializerOptions { WriteIndented = true });
        var jsonFilePath = Path.Combine(_testDataDirectory, "test_data.json");
        await File.WriteAllTextAsync(jsonFilePath, jsonContent);
        
        // Act
        var result = await _dataService.LoadDataAsync(jsonFilePath, CancellationToken.None);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(2, result.Count);
        
        var firstRow = result[0];
        Assert.AreEqual("23001", firstRow.期号);
        Assert.AreEqual(new DateTime(2023, 1, 1), firstRow.开奖日期);
        CollectionAssert.AreEqual(new[] { 1, 5, 12, 18, 25 }, firstRow.红球);
        CollectionAssert.AreEqual(new[] { 3, 8 }, firstRow.蓝球);
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithNonExistentFile_ShouldThrowException()
    {
        // Arrange
        var nonExistentFilePath = Path.Combine(_testDataDirectory, "non_existent.csv");
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<FileNotFoundException>(
            () => _dataService.LoadDataAsync(nonExistentFilePath, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithInvalidCsvFormat_ShouldThrowException()
    {
        // Arrange
        var invalidCsvContent = "invalid,csv,format\n1,2";
        var csvFilePath = Path.Combine(_testDataDirectory, "invalid.csv");
        await File.WriteAllTextAsync(csvFilePath, invalidCsvContent);
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<DataProcessingException>(
            () => _dataService.LoadDataAsync(csvFilePath, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithInvalidRedBallNumbers_ShouldThrowException()
    {
        // Arrange
        var csvContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                        "23001,2023-01-01,01,05,12,18,99,03,08"; // 红球99超出范围
        
        var csvFilePath = Path.Combine(_testDataDirectory, "invalid_red_ball.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<DataProcessingException>(
            () => _dataService.LoadDataAsync(csvFilePath, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithDuplicateRedBalls_ShouldThrowException()
    {
        // Arrange
        var csvContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                        "23001,2023-01-01,01,05,05,18,25,03,08"; // 红球重复
        
        var csvFilePath = Path.Combine(_testDataDirectory, "duplicate_red_balls.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<DataProcessingException>(
            () => _dataService.LoadDataAsync(csvFilePath, CancellationToken.None));
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithCaching_ShouldUseCachedData()
    {
        // Arrange
        var csvContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                        "23001,2023-01-01,01,05,12,18,25,03,08";
        
        var csvFilePath = Path.Combine(_testDataDirectory, "cached_test.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        // Act - 第一次加载
        var result1 = await _dataService.LoadDataAsync(csvFilePath, CancellationToken.None);
        
        // 修改文件内容
        var modifiedContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                             "23002,2023-01-03,02,07,15,22,28,05,11";
        await File.WriteAllTextAsync(csvFilePath, modifiedContent);
        
        // Act - 第二次加载（应该使用缓存）
        var result2 = await _dataService.LoadDataAsync(csvFilePath, CancellationToken.None);
        
        // Assert - 结果应该相同（来自缓存）
        Assert.AreEqual(result1.Count, result2.Count);
        Assert.AreEqual(result1[0].期号, result2[0].期号);
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithCancellation_ShouldThrowOperationCanceledException()
    {
        // Arrange
        var csvContent = "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2\n" +
                        "23001,2023-01-01,01,05,12,18,25,03,08";
        
        var csvFilePath = Path.Combine(_testDataDirectory, "cancellation_test.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        var cts = new CancellationTokenSource();
        cts.Cancel(); // 立即取消
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<OperationCanceledException>(
            () => _dataService.LoadDataAsync(csvFilePath, cts.Token));
    }
    
    [TestMethod]
    public void ValidateConfiguration_WithValidConfig_ShouldNotThrow()
    {
        // Arrange
        var validConfig = new LotteryConfig
        {
            DataPath = _testDataDirectory,
            SequenceLength = 10,
            BacktestPeriods = 50
        };
        
        // Act & Assert - 不应该抛出异常
        var dataService = new DataService(_mockLogger.Object, validConfig, _mockExceptionHandler.Object);
        Assert.IsNotNull(dataService);
    }
    
    [TestMethod]
    public void ValidateConfiguration_WithInvalidSequenceLength_ShouldThrow()
    {
        // Arrange
        var invalidConfig = new LotteryConfig
        {
            DataPath = _testDataDirectory,
            SequenceLength = 0, // 无效值
            BacktestPeriods = 50
        };
        
        // Act & Assert
        Assert.ThrowsException<ArgumentException>(
            () => new DataService(_mockLogger.Object, invalidConfig, _mockExceptionHandler.Object));
    }
    
    [TestMethod]
    public void ValidateConfiguration_WithInvalidBacktestPeriods_ShouldThrow()
    {
        // Arrange
        var invalidConfig = new LotteryConfig
        {
            DataPath = _testDataDirectory,
            SequenceLength = 10,
            BacktestPeriods = -1 // 无效值
        };
        
        // Act & Assert
        Assert.ThrowsException<ArgumentException>(
            () => new DataService(_mockLogger.Object, invalidConfig, _mockExceptionHandler.Object));
    }
    
    [TestMethod]
    public async Task LoadDataAsync_WithLargeDataset_ShouldHandleEfficiently()
    {
        // Arrange
        var largeDataset = new List<string> { "期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2" };
        
        // 生成1000条测试数据
        for (int i = 1; i <= 1000; i++)
        {
            var period = $"23{i:D3}";
            var date = new DateTime(2023, 1, 1).AddDays(i - 1).ToString("yyyy-MM-dd");
            largeDataset.Add($"{period},{date},01,05,12,18,25,03,08");
        }
        
        var csvContent = string.Join(Environment.NewLine, largeDataset);
        var csvFilePath = Path.Combine(_testDataDirectory, "large_dataset.csv");
        await File.WriteAllTextAsync(csvFilePath, csvContent);
        
        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _dataService.LoadDataAsync(csvFilePath, CancellationToken.None);
        stopwatch.Stop();
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(1000, result.Count);
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, $"加载时间过长: {stopwatch.ElapsedMilliseconds}ms");
    }
    
    [TestMethod]
    public void Dispose_ShouldCleanupResources()
    {
        // Arrange
        var dataService = new DataService(_mockLogger.Object, _testConfig, _mockExceptionHandler.Object);
        
        // Act
        dataService.Dispose();
        
        // Assert - 验证日志记录了释放操作
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("数据服务已释放")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.AtLeastOnce);
    }
}