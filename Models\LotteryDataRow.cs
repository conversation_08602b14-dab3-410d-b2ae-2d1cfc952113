using Microsoft.ML.Data;

namespace DLT_CP.Models;

/// <summary>
/// 大乐透数据行模型
/// </summary>
public class LotteryDataRow
{
    [LoadColumn(0)]
    public string PeriodNumber { get; set; } = string.Empty;
    
    [LoadColumn(1)]
    public DateTime DrawDate { get; set; }
    
    [LoadColumn(2)]
    public int Red1 { get; set; }
    
    [LoadColumn(3)]
    public int Red2 { get; set; }
    
    [LoadColumn(4)]
    public int Red3 { get; set; }
    
    [LoadColumn(5)]
    public int Red4 { get; set; }
    
    [LoadColumn(6)]
    public int Red5 { get; set; }
    
    [LoadColumn(7)]
    public int Blue1 { get; set; }
    
    [LoadColumn(8)]
    public int Blue2 { get; set; }
    
    /// <summary>
    /// 获取红球数组
    /// </summary>
    public int[] RedBalls => new[] { Red1, Red2, Red3, Red4, Red5 };
    
    /// <summary>
    /// 获取蓝球数组
    /// </summary>
    public int[] BlueBalls => new[] { Blue1, Blue2 };
    
    /// <summary>
    /// 计算红球奇偶比例 (奇数个数, 偶数个数)
    /// </summary>
    public (int Odd, int Even) RedOddEvenRatio
    {
        get
        {
            var odd = RedBalls.Count(x => x % 2 == 1);
            return (odd, 5 - odd);
        }
    }
    
    /// <summary>
    /// 计算红球大小比例 (大号个数, 小号个数)
    /// 大号: 18-35, 小号: 1-17
    /// </summary>
    public (int Large, int Small) RedSizeRatio
    {
        get
        {
            var large = RedBalls.Count(x => x >= 18);
            return (large, 5 - large);
        }
    }
    
    /// <summary>
    /// 计算蓝球大小比例 (大号个数, 小号个数)
    /// 大号: 7-12, 小号: 1-6
    /// </summary>
    public (int Large, int Small) BlueSizeRatio
    {
        get
        {
            var large = BlueBalls.Count(x => x >= 7);
            return (large, 2 - large);
        }
    }
    
    /// <summary>
    /// 计算蓝球奇偶比例 (奇数个数, 偶数个数)
    /// </summary>
    public (int Odd, int Even) BlueOddEvenRatio
    {
        get
        {
            var odd = BlueBalls.Count(x => x % 2 == 1);
            return (odd, 2 - odd);
        }
    }
}