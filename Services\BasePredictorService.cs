using DLT_CP.Models;
using Microsoft.Extensions.Logging;
using DLT_CP.Exceptions;
using System.Diagnostics;

namespace DLT_CP.Services;

/// <summary>
/// 预测器基类
/// </summary>
public abstract class BasePredictorService : IPredictor, IDisposable
{
    protected readonly ILogger _logger;
    protected readonly Dictionary<string, object> _parameters = new();
    protected readonly Dictionary<string, double> _metrics = new();
    private readonly GlobalExceptionHandler? _exceptionHandler;
    
    protected BasePredictorService(ILogger logger, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _exceptionHandler = exceptionHandler;
    }
    
    public abstract string Name { get; }
    public abstract string Description { get; }
    public virtual bool IsTrained { get; protected set; }
    
    public abstract Task<bool> TrainAsync(IEnumerable<LotteryDataRow> data, CancellationToken cancellationToken = default);
    
    public abstract Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> historyData, string nextPeriod, CancellationToken cancellationToken = default);
    
    public virtual async Task<IEnumerable<PredictionResult>> PredictBatchAsync(IEnumerable<LotteryDataRow> historyData, IEnumerable<string> periods, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new List<PredictionResult>();
        var dataList = historyData.ToList();
        
        _logger.LogInformation("开始批量预测 - 预测器: {PredictorName}, 期数: {PeriodCount}", Name, periods.Count());
        
        foreach (var period in periods)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning("批量预测被取消 - 预测器: {PredictorName}, 已完成: {CompletedCount}/{TotalCount}", 
                    Name, results.Count, periods.Count());
                break;
            }
                
            try
            {
                var periodStopwatch = Stopwatch.StartNew();
                var result = await PredictAsync(dataList, period, cancellationToken);
                periodStopwatch.Stop();
                
                results.Add(result);
                
                _logger.LogDebug("预测完成 - 期号: {Period}, 耗时: {Duration}ms", 
                    period, periodStopwatch.ElapsedMilliseconds);
                
                // 记录性能警告
                _exceptionHandler?.LogPerformanceWarning(
                    $"单期预测-{Name}", 
                    periodStopwatch.Elapsed, 
                    $"期号: {period}");
                
                // 将预测结果添加到历史数据中，用于下一次预测
                // 注意：这里只是模拟，实际应用中需要等待真实开奖结果
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("预测期号 {Period} 被取消", period);
                throw;
            }
            catch (Exception ex)
            {
                var predictionEx = new PredictionException(
                    $"预测期号 {period} 时发生错误: {ex.Message}", 
                    Name, 
                    ex, 
                    period);
                
                _exceptionHandler?.HandleBusinessException(predictionEx, $"批量预测-期号{period}");
                
                // 根据配置决定是否继续处理其他期号
                if (ShouldStopOnError())
                {
                    throw predictionEx;
                }
            }
        }
        
        stopwatch.Stop();
        
        _logger.LogInformation("批量预测完成 - 预测器: {PredictorName}, 成功: {SuccessCount}/{TotalCount}, 总耗时: {TotalDuration}ms", 
            Name, results.Count, periods.Count(), stopwatch.ElapsedMilliseconds);
        
        // 记录批量预测性能
        _exceptionHandler?.LogPerformanceWarning(
            $"批量预测-{Name}", 
            stopwatch.Elapsed, 
            $"期数: {periods.Count()}, 成功: {results.Count}");
        
        return results;
    }
    
    public virtual Dictionary<string, object> GetModelParameters()
    {
        return new Dictionary<string, object>(_parameters);
    }
    
    public virtual void SetModelParameters(Dictionary<string, object> parameters)
    {
        foreach (var param in parameters)
        {
            _parameters[param.Key] = param.Value;
        }
    }
    
    public abstract Task SaveModelAsync(string filePath, CancellationToken cancellationToken = default);
    
    public abstract Task LoadModelAsync(string filePath, CancellationToken cancellationToken = default);
    
    public virtual Dictionary<string, double> GetEvaluationMetrics()
    {
        return new Dictionary<string, double>(_metrics);
    }
    
    /// <summary>
    /// 验证输入数据
    /// </summary>
    protected virtual bool ValidateInputData(IEnumerable<LotteryDataRow> data, string context = "")
    {
        try
        {
            if (data == null)
            {
                _logger.LogError("输入数据为空 - 上下文: {Context}", context);
                return false;
            }
            
            var dataList = data.ToList();
            if (dataList.Count == 0)
            {
                _logger.LogError("输入数据集为空 - 上下文: {Context}", context);
                return false;
            }
            
            // 检查数据完整性
            var invalidRows = dataList.Where(row => 
                string.IsNullOrWhiteSpace(row.PeriodNumber) ||
                row.RedBalls.Any(r => r < 1 || r > 35) ||
                row.BlueBalls.Any(b => b < 1 || b > 12)).ToList();
            
            if (invalidRows.Any())
            {
                _logger.LogWarning("发现 {InvalidCount} 条无效数据记录 - 上下文: {Context}", 
                    invalidRows.Count, context);
                
                foreach (var row in invalidRows.Take(5)) // 只记录前5条
                {
                    _logger.LogDebug("无效数据 - 期号: {Period}, 红球: [{RedBalls}], 蓝球: [{BlueBalls}]", 
                        row.PeriodNumber, string.Join(",", row.RedBalls), string.Join(",", row.BlueBalls));
                }
            }
            
            _logger.LogDebug("数据验证通过 - 总数: {TotalCount}, 有效: {ValidCount}, 上下文: {Context}", 
                dataList.Count, dataList.Count - invalidRows.Count, context);
            
            return true;
        }
        catch (Exception ex)
        {
            var dataEx = new DataProcessingException(
                $"数据验证过程中发生错误: {ex.Message}", 
                context, 
                ex);
            
            _exceptionHandler?.HandleBusinessException(dataEx, $"数据验证-{Name}");
            return false;
        }
    }
    
    /// <summary>
    /// 验证单行彩票数据
    /// </summary>
    protected virtual bool IsValidLotteryRow(LotteryDataRow row)
    {
        // 检查红球
        if (row.RedBalls.Length != 5)
            return false;
            
        if (row.RedBalls.Any(x => x < 1 || x > 35))
            return false;
            
        if (row.RedBalls.Distinct().Count() != 5)
            return false;
            
        // 检查蓝球
        if (row.BlueBalls.Length != 2)
            return false;
            
        if (row.BlueBalls.Any(x => x < 1 || x > 12))
            return false;
            
        if (row.BlueBalls.Distinct().Count() != 2)
            return false;
            
        return true;
    }
    
    /// <summary>
    /// 生成随机红球
    /// </summary>
    protected virtual int[] GenerateRandomRedBalls(Random? random = null)
    {
        random ??= new Random();
        var balls = new HashSet<int>();
        
        while (balls.Count < 5)
        {
            balls.Add(random.Next(1, 36));
        }
        
        return balls.OrderBy(x => x).ToArray();
    }
    
    /// <summary>
    /// 生成随机蓝球
    /// </summary>
    protected virtual int[] GenerateRandomBlueBalls(Random? random = null)
    {
        random ??= new Random();
        var balls = new HashSet<int>();
        
        while (balls.Count < 2)
        {
            balls.Add(random.Next(1, 13));
        }
        
        return balls.OrderBy(x => x).ToArray();
    }
    
    /// <summary>
    /// 计算球号频率
    /// </summary>
    protected virtual Dictionary<int, int> CalculateBallFrequency(IEnumerable<LotteryDataRow> data, bool isRedBall)
    {
        var frequency = new Dictionary<int, int>();
        var maxBall = isRedBall ? 35 : 12;
        
        // 初始化
        for (int i = 1; i <= maxBall; i++)
        {
            frequency[i] = 0;
        }
        
        // 统计频率
        foreach (var row in data)
        {
            var balls = isRedBall ? row.RedBalls : row.BlueBalls;
            foreach (var ball in balls)
            {
                if (frequency.ContainsKey(ball))
                {
                    frequency[ball]++;
                }
            }
        }
        
        return frequency;
    }
    
    /// <summary>
    /// 更新评估指标
    /// </summary>
    protected virtual void UpdateMetrics(string key, double value)
    {
        _metrics[key] = value;
    }
    
    /// <summary>
    /// 是否在错误时停止处理
    /// </summary>
    protected virtual bool ShouldStopOnError()
    {
        return _parameters.ContainsKey("StopOnError") && 
               Convert.ToBoolean(_parameters["StopOnError"]);
    }
    
    /// <summary>
    /// 记录训练指标
    /// </summary>
    protected void LogTrainingMetrics(string phase, Dictionary<string, double>? additionalMetrics = null)
    {
        var metricsLog = new List<string>();
        
        foreach (var metric in _metrics)
        {
            metricsLog.Add($"{metric.Key}: {metric.Value:F4}");
        }
        
        if (additionalMetrics != null)
        {
            foreach (var metric in additionalMetrics)
            {
                metricsLog.Add($"{metric.Key}: {metric.Value:F4}");
            }
        }
        
        _logger.LogInformation("训练指标 - 预测器: {PredictorName}, 阶段: {Phase}, 指标: {Metrics}", 
            Name, phase, string.Join(", ", metricsLog));
    }
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    /// <summary>
    /// 释放资源的虚方法
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        try
        {
            if (disposing)
            {
                _logger.LogDebug("正在释放预测器资源 - {PredictorName}", Name);
                
                // 清理参数和指标
                _parameters?.Clear();
                _metrics?.Clear();
                
                _logger.LogDebug("预测器资源释放完成 - {PredictorName}", Name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放预测器资源时发生错误 - {PredictorName}", Name);
        }
    }
}