using DLT_CP.Models;
using Microsoft.Extensions.Logging;

namespace DLT_CP.Services;

/// <summary>
/// 频率分析预测器
/// 基于历史号码出现频率进行预测
/// </summary>
public class FrequencyAnalyzer : BasePredictorService
{
    private Dictionary<int, int> _redBallFrequency = new();
    private Dictionary<int, int> _blueBallFrequency = new();
    private Dictionary<int, DateTime> _redBallLastAppearance = new();
    private Dictionary<int, DateTime> _blueBallLastAppearance = new();
    private int _analysisWindow = 100; // 分析窗口期数
    
    public FrequencyAnalyzer(ILogger<FrequencyAnalyzer> logger) : base(logger)
    {
        // 设置默认参数
        _parameters["AnalysisWindow"] = _analysisWindow;
        _parameters["HotBallThreshold"] = 0.7; // 热号阈值
        _parameters["ColdBallThreshold"] = 0.3; // 冷号阈值
        _parameters["UseLastAppearance"] = true; // 是否考虑最后出现时间
    }
    
    public override string Name => "频率分析预测器";
    
    public override string Description => "基于历史号码出现频率和遗漏分析进行预测，结合热号和冷号的统计规律";
    
    public override async Task<bool> TrainAsync(IEnumerable<LotteryDataRow> data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始训练频率分析模型...");
            
            if (!ValidateInputData(data))
            {
                return false;
            }
            
            var dataList = data.OrderBy(x => x.DrawDate).ToList();
            
            // 获取分析窗口
            if (_parameters.ContainsKey("AnalysisWindow"))
            {
                _analysisWindow = Convert.ToInt32(_parameters["AnalysisWindow"]);
            }
            
            // 取最近的分析窗口期数据
            var analysisData = dataList.TakeLast(_analysisWindow).ToList();
            
            // 计算频率
            _redBallFrequency = CalculateBallFrequency(analysisData, true);
            _blueBallFrequency = CalculateBallFrequency(analysisData, false);
            
            // 计算最后出现时间
            CalculateLastAppearance(analysisData);
            
            // 计算评估指标
            CalculateMetrics(analysisData);
            
            IsTrained = true;
            _logger.LogInformation("频率分析模型训练完成");
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练频率分析模型时发生错误");
            return false;
        }
    }
    
    public override async Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> historyData, string nextPeriod, CancellationToken cancellationToken = default)
    {
        if (!IsTrained)
        {
            await TrainAsync(historyData, cancellationToken);
        }
        
        try
        {
            _logger.LogInformation("开始预测期号: {Period}", nextPeriod);
            
            var dataList = historyData.OrderBy(x => x.DrawDate).ToList();
            var latestData = dataList.TakeLast(_analysisWindow).ToList();
            
            // 重新计算最新频率
            var currentRedFreq = CalculateBallFrequency(latestData, true);
            var currentBlueFreq = CalculateBallFrequency(latestData, false);
            
            // 预测红球
            var predictedRed = PredictRedBalls(currentRedFreq, latestData);
            
            // 预测蓝球
            var predictedBlue = PredictBlueBalls(currentBlueFreq, latestData);
            
            // 计算置信度
            var confidence = CalculateConfidence(predictedRed, predictedBlue, latestData);
            
            var result = new PredictionResult
            {
                PeriodNumber = nextPeriod,
                PredictionTime = DateTime.Now,
                RedBalls = predictedRed,
                BlueBalls = predictedBlue,
                Confidence = confidence,
                ModelName = Name,
                HistoryPeriods = latestData.Count,
                ModelScore = _metrics.ContainsKey("OverallScore") ? _metrics["OverallScore"] : 0,
                Notes = $"基于最近{_analysisWindow}期数据的频率分析"
            };
            
            _logger.LogInformation("预测完成: {Numbers}", result.FullNumberString);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测时发生错误");
            throw;
        }
    }
    
    private int[] PredictRedBalls(Dictionary<int, int> frequency, List<LotteryDataRow> recentData)
    {
        var hotThreshold = Convert.ToDouble(_parameters["HotBallThreshold"]);
        var coldThreshold = Convert.ToDouble(_parameters["ColdBallThreshold"]);
        var useLastAppearance = Convert.ToBoolean(_parameters["UseLastAppearance"]);
        
        // 计算平均频率
        var avgFreq = frequency.Values.Average();
        
        // 分类号码
        var hotBalls = frequency.Where(x => x.Value >= avgFreq * hotThreshold).Select(x => x.Key).ToList();
        var coldBalls = frequency.Where(x => x.Value <= avgFreq * coldThreshold).Select(x => x.Key).ToList();
        var normalBalls = frequency.Where(x => x.Value > avgFreq * coldThreshold && x.Value < avgFreq * hotThreshold)
                                  .Select(x => x.Key).ToList();
        
        var selectedBalls = new HashSet<int>();
        var random = new Random();
        
        // 策略：选择2个热号，2个冷号，1个正常号
        // 热号选择
        var hotCandidates = hotBalls.OrderByDescending(x => frequency[x]).Take(Math.Min(8, hotBalls.Count)).ToList();
        while (selectedBalls.Count < 2 && hotCandidates.Any())
        {
            var ball = hotCandidates[random.Next(hotCandidates.Count)];
            selectedBalls.Add(ball);
            hotCandidates.Remove(ball);
        }
        
        // 冷号选择（考虑遗漏期数）
        if (useLastAppearance)
        {
            var latestDate = recentData.Max(x => x.DrawDate);
            coldBalls = coldBalls.OrderBy(x => _redBallLastAppearance.ContainsKey(x) ? 
                                       (latestDate - _redBallLastAppearance[x]).Days : int.MaxValue)
                                .Take(Math.Min(8, coldBalls.Count)).ToList();
        }
        
        while (selectedBalls.Count < 4 && coldBalls.Any())
        {
            var ball = coldBalls[random.Next(coldBalls.Count)];
            if (selectedBalls.Add(ball))
            {
                coldBalls.Remove(ball);
            }
        }
        
        // 正常号选择
        while (selectedBalls.Count < 5 && normalBalls.Any())
        {
            var ball = normalBalls[random.Next(normalBalls.Count)];
            if (selectedBalls.Add(ball))
            {
                normalBalls.Remove(ball);
            }
        }
        
        // 如果还不够5个，从剩余号码中随机选择
        if (selectedBalls.Count < 5)
        {
            var remainingBalls = Enumerable.Range(1, 35).Except(selectedBalls).ToList();
            while (selectedBalls.Count < 5 && remainingBalls.Any())
            {
                var ball = remainingBalls[random.Next(remainingBalls.Count)];
                selectedBalls.Add(ball);
                remainingBalls.Remove(ball);
            }
        }
        
        return selectedBalls.OrderBy(x => x).ToArray();
    }
    
    private int[] PredictBlueBalls(Dictionary<int, int> frequency, List<LotteryDataRow> recentData)
    {
        var selectedBalls = new HashSet<int>();
        var random = new Random();
        
        // 按频率排序，选择频率较高的号码
        var sortedBalls = frequency.OrderByDescending(x => x.Value).ToList();
        
        // 从前6个高频号码中随机选择1个
        var topBalls = sortedBalls.Take(6).Select(x => x.Key).ToList();
        selectedBalls.Add(topBalls[random.Next(topBalls.Count)]);
        
        // 从剩余号码中选择1个
        var remainingBalls = Enumerable.Range(1, 12).Except(selectedBalls).ToList();
        selectedBalls.Add(remainingBalls[random.Next(remainingBalls.Count)]);
        
        return selectedBalls.OrderBy(x => x).ToArray();
    }
    
    private void CalculateLastAppearance(List<LotteryDataRow> data)
    {
        _redBallLastAppearance.Clear();
        _blueBallLastAppearance.Clear();
        
        foreach (var row in data.OrderBy(x => x.DrawDate))
        {
            foreach (var ball in row.RedBalls)
            {
                _redBallLastAppearance[ball] = row.DrawDate;
            }
            
            foreach (var ball in row.BlueBalls)
            {
                _blueBallLastAppearance[ball] = row.DrawDate;
            }
        }
    }
    
    private double CalculateConfidence(int[] redBalls, int[] blueBalls, List<LotteryDataRow> recentData)
    {
        // 基于频率分布的置信度计算
        var redConfidence = redBalls.Average(ball => 
            _redBallFrequency.ContainsKey(ball) ? _redBallFrequency[ball] / (double)recentData.Count : 0);
        
        var blueConfidence = blueBalls.Average(ball => 
            _blueBallFrequency.ContainsKey(ball) ? _blueBallFrequency[ball] / (double)recentData.Count : 0);
        
        return (redConfidence * 0.7 + blueConfidence * 0.3) * 100;
    }
    
    private void CalculateMetrics(List<LotteryDataRow> data)
    {
        // 计算频率分布的均匀性
        var redVariance = CalculateVariance(_redBallFrequency.Values);
        var blueVariance = CalculateVariance(_blueBallFrequency.Values);
        
        UpdateMetrics("RedFrequencyVariance", redVariance);
        UpdateMetrics("BlueFrequencyVariance", blueVariance);
        UpdateMetrics("OverallScore", Math.Max(0, 100 - (redVariance + blueVariance) * 10));
        UpdateMetrics("DataPeriods", data.Count);
    }
    
    private double CalculateVariance(IEnumerable<int> values)
    {
        var mean = values.Average();
        return values.Select(x => Math.Pow(x - mean, 2)).Average();
    }
    
    public override async Task SaveModelAsync(string filePath, CancellationToken cancellationToken = default)
    {
        // 实现模型保存逻辑
        await Task.CompletedTask;
    }
    
    public override async Task LoadModelAsync(string filePath, CancellationToken cancellationToken = default)
    {
        // 实现模型加载逻辑
        await Task.CompletedTask;
    }
}