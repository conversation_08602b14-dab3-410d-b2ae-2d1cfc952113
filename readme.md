大乐透预测系统开发文档 (C#/.NET版)
1. 项目概述
1.1. 项目简介
大乐透预测系统是一个基于数据分析和概率模型的彩票预测系统。本项目采用 C# 和 .NET 平台构建，利用 ML.NET 进行传统机器学习建模，并集成 TorchSharp 以支持深度学习模型。系统通过分析历史开奖数据，进行特征工程、模型训练、推理、后处理（如杀号），并生成最终的号码组合。系统包含一个完整的回测框架，用于验证模型在历史数据上的表现。

大小比例按照先大后小，红球大号(18-35),红球小号（1-17），蓝球大号（7-12），小号（1-6），使用WPF .NET9.0框架。
奇偶比例按照先奇后偶。



1.2. 核心特性
强类型与面向对象: 利用 C# 的强类型系统和面向对象特性，构建健壮、可维护的系统。

高性能: 基于 .NET 平台，为数据处理和模型推理提供高性能支持。

模块化架构: 遵循 SOLID 原则，通过依赖注入实现高内聚、低耦合的设计。

多算法支持: 集成 ML.NET 和 TorchSharp，支持从经典机器学习到前沿深度学习的多种预测算法。

统一框架: 提供标准化的预测器接口 (IPredictor) 和回测框架 (BacktestFramework)。

确定性预测: 算法设计避免随机性（在模型训练之外），确保对于同一份输入数据和模型，预测结果总是可重现的。

1.3. 验证标准
比值预测: 奇偶、大小等比值预测的准确率（三项命中率）≥ 80%。

杀号逻辑: 杀号算法在回测中的成功率 ≥ 90%。

号码生成器: 在最近50期回测中，命中2+1的次数占比 ≥ 60%。

2. 技术架构
2.1. 技术栈
开发平台: .NET 8+

编程语言: C# 12+

机器学习: ML.NET (用于回归、分类等任务)

深度学习: TorchSharp (用于 LSTM, Transformer 等神经网络模型)

数据处理: LINQ, System.Data.DataTable, Deedle (可选，用于类似DataFrame的操作)

依赖注入: Microsoft.Extensions.DependencyInjection

配置管理: Microsoft.Extensions.Configuration (appsettings.json)

测试框架: xUnit / NUnit / MSTest

代码质量: .NET Analyzers, StyleCop

数据库: Entity Framework Core (可选，用于存储历史数据和预测结果)

2.2. .NET 解决方案结构
推荐使用分层架构，将不同的职责分离到独立的项目中。

/LotteryPredictor.sln
├── src/
│   ├── LotteryPredictor.ConsoleApp/     # 主应用程序入口 (控制台)
│   ├── LotteryPredictor.Core/           # 核心业务逻辑 (接口、DTO、领域模型)
│   ├── LotteryPredictor.Data/           # 数据访问层 (数据加载、仓储)
│   ├── LotteryPredictor.Services/       # 服务层 (分析器、生成器)
│   ├── LotteryPredictor.Models.MLNet/   # 基于 ML.NET 的预测模型
│   ├── LotteryPredictor.Models.Torch/   # 基于 TorchSharp 的预测模型
│   └── LotteryPredictor.Framework/      # 统一框架 (回测、评估)
│
├── tests/
│   ├── LotteryPredictor.Core.Tests/     # 核心模块单元测试
│   ├── LotteryPredictor.Services.Tests/ # 服务模块单元测试
│   └── LotteryPredictor.Framework.Tests/# 框架模块单元测试
│
├── config/                              # 配置文件 (如 appsettings.json)
├── data/                                # 原始数据文件 (如 dlt_data.csv)
├── docs/                                # 项目文档
└── README.md                            # 项目说明

3. 核心模块详解
3.1. 核心模块 (LotteryPredictor.Core)
数据传输对象 (DTOs)
使用 record 类型定义不可变的数据模型。

// DTOs/PredictionResult.cs
public record PredictionResult(
    string PeriodNumber,
    Dictionary<string, double> RedOddEvenPredictions,
    Dictionary<string, double> RedSizePredictions,
    Dictionary<string, double> BlueSizePredictions,
    (List<int> RedBalls, List<int> BlueBalls) GeneratedNumbers
);

// DTOs/LotteryDataRow.cs
public class LotteryDataRow
{
    [LoadColumn(0)] public string PeriodNumber { get; set; }
    [LoadColumn(1)] public DateTime DrawDate { get; set; }
    // ... 其他列
}

核心接口 (Interfaces)
定义系统各模块必须遵循的契约。

// Interfaces/IPredictor.cs
public interface IPredictor
{
    /// <summary>
    /// 训练模型
    /// </summary>
    /// <param name="trainingData">用于训练的历史数据</param>
    Task TrainAsync(IEnumerable<LotteryDataRow> trainingData);

    /// <summary>
    /// 执行预测
    /// </summary>
    /// <param name="contextData">用于预测的近期数据</param>
    /// <returns>预测结果</returns>
    Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> contextData);
}

3.2. 服务模块 (LotteryPredictor.Services)
分析器 (LotteryAnalyzer.cs)
提供对彩票数据的统计分析功能。

public class LotteryAnalyzer : ILotteryAnalyzer
{
    public Dictionary<string, double> AnalyzeOddEvenRatio(IEnumerable<LotteryDataRow> data)
    {
        // ... 实现奇偶比分析逻辑
    }

    public Dictionary<string, double> AnalyzeSizeRatio(IEnumerable<LotteryDataRow> data)
    {
        // ... 实现大小比分析逻辑
    }
}

3.3. 预测模型
ML.NET 模型 (LotteryPredictor.Models.MLNet)
使用 ML.NET 实现经典的机器学习模型。

// MLNet/OddEvenRatioPredictor.cs
public class OddEvenRatioPredictor : IPredictor
{
    private readonly MLContext _mlContext;
    private ITransformer _model;

    public OddEvenRatioPredictor()
    {
        _mlContext = new MLContext(seed: 0); // 使用种子确保可重现
    }

    public async Task TrainAsync(IEnumerable<LotteryDataRow> trainingData)
    {
        // 1. 构建训练管道 (Pipeline)
        var pipeline = _mlContext.Transforms.Conversion.MapValueToKey("Label")
            .Append(...) // 特征工程
            .Append(_mlContext.MulticlassClassification.Trainers.SdcaMaximumEntropy());

        // 2. 训练模型
        var dataView = _mlContext.Data.LoadFromEnumerable(trainingData);
        _model = pipeline.Fit(dataView);
        
        // 3. (可选) 保存模型
        // _mlContext.Model.Save(_model, dataView.Schema, "model.zip");
    }

    public async Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> contextData)
    {
        // ... 使用 _model 进行预测
    }
}

TorchSharp 模型 (LotteryPredictor.Models.Torch)
使用 TorchSharp 构建和训练神经网络。

// Torch/LSTMPredictor.cs
using static TorchSharp.torch;

public class LSTMPredictor : Module<Tensor, Tensor>, IPredictor
{
    private readonly LSTM _lstm;
    private readonly Linear _linear;
    private Device _device;

    public LSTMPredictor(string name, int inputSize, int hiddenSize, int numLayers) : base(name)
    {
        _lstm = nn.LSTM(inputSize, hiddenSize, numLayers, batchFirst: true);
        _linear = nn.Linear(hiddenSize, 1);
        _device = cuda.is_available() ? CUDA : CPU;
        this.to(_device);
    }
    
    public override Tensor forward(Tensor input)
    {
        var (output, _) = _lstm.forward(input);
        return _linear.forward(output[.., -1, ..]); // 取序列最后一个输出
    }

    public async Task TrainAsync(IEnumerable<LotteryDataRow> trainingData)
    {
        // 1. 将数据转换为 Tensor
        // 2. 定义损失函数和优化器
        var criterion = nn.MSELoss();
        var optimizer = optim.Adam(this.parameters(), lr: 0.001);

        // 3. 训练循环...
    }
    
    // ... 实现 IPredictor 的 PredictAsync 方法
}

3.4. 统一框架 (LotteryPredictor.Framework)
回测框架 (BacktestFramework.cs)
提供统一的回测流程，用于评估任何实现了 IPredictor 接口的模型。

public class BacktestFramework
{
    public async Task<BacktestResult> RunBacktestAsync(
        IPredictor predictor, 
        BacktestConfig config, 
        IEnumerable<LotteryDataRow> allHistoricalData)
    {
        var results = new List<EvaluationMetric>();
        var totalPeriods = allHistoricalData.Count();

        for (int i = 0; i < config.BacktestPeriods; i++)
        {
            var splitPoint = totalPeriods - config.BacktestPeriods + i;
            var trainingData = allHistoricalData.Take(splitPoint);
            var actualData = allHistoricalData.ElementAt(splitPoint);
            
            // 每次回测都重新训练模型，以模拟真实场景
            await predictor.TrainAsync(trainingData);
            var prediction = await predictor.PredictAsync(trainingData);
            
            // 评估预测结果和真实值的差异
            var metric = Evaluate(prediction, actualData);
            results.Add(metric);
        }
        
        return new BacktestResult(results);
    }

    private EvaluationMetric Evaluate(PredictionResult prediction, LotteryDataRow actual)
    {
        // ... 实现评估逻辑
    }
}

4. 开发指南
4.1. 环境配置
安装 .NET SDK: 安装 .NET 8 SDK。

IDE: 推荐使用 Visual Studio 2022 或 JetBrains Rider。

克隆项目:

git clone <repository-url>
cd LotteryPredictor

安装依赖:
项目依赖通过 NuGet 管理，在项目目录运行 dotnet restore 即可。

# 安装 ML.NET
dotnet add package Microsoft.ML

# 安装 TorchSharp
dotnet add package TorchSharp

4.2. 配置管理
所有配置项都应放在 appsettings.json 中，并通过 .NET 的配置系统读取。

appsettings.json:

{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=data/lottery.db"
  },
  "LotteryConfig": {
    "DataPath": "data/dlt_data.csv",
    "SequenceLength": 20,
    "BacktestPeriods": 50
  },
  "ModelConfigs": {
    "MarkovOrder": 2,
    "NeuralEpochs": 100
  }
}

C# 配置类:

public class LotteryConfig
{
    public string DataPath { get; set; }
    public int SequenceLength { get; set; }
    public int BacktestPeriods { get; set; }
}

在 Program.cs 中注入配置:

public static IHostBuilder CreateHostBuilder(string[] args) =>
    Host.CreateDefaultBuilder(args)
        .ConfigureServices((hostContext, services) =>
        {
            // 注册配置
            services.Configure<LotteryConfig>(hostContext.Configuration.GetSection("LotteryConfig"));
            
            // 注册服务
            services.AddSingleton<ILotteryAnalyzer, LotteryAnalyzer>();
            services.AddTransient<IPredictor, LSTMPredictor>();
        });

4.3. 代码规范与原则
命名规范: 遵循 Microsoft 的 C# 编码约定，如 PascalCase 用于类、方法和属性。

SOLID 原则:

单一职责: 每个类只做一件事。

开闭原则: 对扩展开放，对修改关闭。通过接口实现。

里氏替换: 子类可以替换父类。

接口隔离: 使用小而专的接口。

依赖倒置: 依赖于抽象（接口），而不是具体实现。

异步编程: 对所有I/O密集型和耗时操作（如文件读写、模型训练）使用 async/await。

4.4. 开发流程
创建功能分支:

git checkout -b feature/new-predictor

编写代码和单元测试。

运行测试:

dotnet test

运行主程序:

dotnet run --project src/LotteryPredictor.ConsoleApp/LotteryPredictor.ConsoleApp.csproj

提交代码并发起 Pull Request。

5. API 参考 (核心接口)
IPredictor 接口
所有预测模型都必须实现的接口，以确保可以被统一框架调用。

public interface IPredictor
{
    /// <summary>
    /// 异步训练模型。
    /// </summary>
    /// <param name="trainingData">用于训练的历史数据集。</param>
    Task TrainAsync(IEnumerable<LotteryDataRow> trainingData);
    
    /// <summary>
    /// 异步执行预测。
    /// </summary>
    /// <param name="contextData">用于生成新预测的上下文数据。</param>
    /// <returns>包含各类预测和生成号码的 PredictionResult 对象。</returns>
    Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> contextData);
}

IBacktestFramework 接口
定义回测框架的行为。

public interface IBacktestFramework
{
    /// <summary>
    /// 异步运行回测。
    /// </summary>
    /// <param name="predictor">要评估的预测器实例。</param>
    /// <param name="config">回测配置。</param>
    /// <param name="allHistoricalData">全部历史数据。</param>
    /// <returns>回测结果的摘要。</returns>
    Task<BacktestResult> RunBacktestAsync(IPredictor predictor, BacktestConfig config, IEnumerable<LotteryDataRow> allHistoricalData);
}

6. 部署指南
6.1. 本地部署
确保已安装 .NET 8 Runtime。

发布项目:

dotnet publish -c Release -o ./publish src/LotteryPredictor.ConsoleApp/

运行已发布的应用:

cd ./publish
./LotteryPredictor.ConsoleApp

6.2. 生产部署 (使用 Docker)
创建 Dockerfile:

# 使用 .NET SDK 镜像构建应用
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /source

COPY . .
RUN dotnet restore "./src/LotteryPredictor.ConsoleApp/LotteryPredictor.ConsoleApp.csproj"
RUN dotnet publish "./src/LotteryPredictor.ConsoleApp/LotteryPredictor.ConsoleApp.csproj" -c Release -o /app/publish --no-restore

# 使用 .NET Runtime 镜像运行应用
FROM mcr.microsoft.com/dotnet/runtime:8.0
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "LotteryPredictor.ConsoleApp.dll"]

构建并运行 Docker 镜像:

docker build -t lottery-predictor .
docker run --rm lottery-predictor

7. 故障排除
常见问题
System.IO.FileNotFoundException:

问题: 无法找到数据文件或配置文件。

解决: 确保 appsettings.json 中配置的路径正确，并且文件已设置为 "Copy to Output Directory" (在 Visual Studio 的文件属性中设置)。

TorchSharp 后端错误:

问题: libtorch 相关的 DllNotFoundException。

解决: 确保 TorchSharp NuGet 包及其依赖项已正确安装。根据操作系统，可能需要安装特定的 libtorch-cpu 或 libtorch-cuda 包。

ML.NET 模型版本不兼容:

问题: 加载已保存的模型时出错。

解决: 确保训练和加载模型时使用的 ML.NET 版本一致。

日志分析
使用 Microsoft.Extensions.Logging 框架（已内置于通用主机中）记录日志。可以在 appsettings.json 中配置日志级别和输出（控制台、文件等）。

8. 贡献与版本
8.1. 贡献指南
欢迎社区贡献！请遵循以下步骤：

Fork 本项目。

创建您的功能分支 (git checkout -b feature/AmazingFeature)。

提交您的更改 (git commit -m 'feat: Add some AmazingFeature')。

将您的分支推送到远程 (git push origin feature/AmazingFeature)。

发起一个 Pull Request。

8.2. 版本历史
v1.0.0:

初始化 C#/.NET 项目架构。

实现基于 ML.NET 和 TorchSharp 的预测器接口。

建立统一的回测框架。

未来规划:

v1.1.0: 优化深度学习模型，引入 Transformer 架构。

v1.2.0: 开发 ASP.NET Core Web API 用于实时预测。

v1.3.0: 构建 Blazor WebAssembly 前端界面进行数据可视化。
