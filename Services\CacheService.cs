using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using DLT_CP.Models;
using DLT_CP.Exceptions;

namespace DLT_CP.Services;

/// <summary>
/// 缓存服务 - 提供多级缓存支持
/// </summary>
public class CacheService : IDisposable
{
    private readonly ILogger<CacheService> _logger;
    private readonly GlobalExceptionHandler? _exceptionHandler;
    
    // 内存缓存
    private readonly ConcurrentDictionary<string, CacheItem> _memoryCache = new();
    
    // 缓存配置
    private readonly TimeSpan _defaultExpiry;
    private readonly long _maxMemoryCacheSize;
    private readonly string _diskCacheDirectory;
    private readonly Timer _cleanupTimer;
    
    // 统计信息
    private long _memoryHits;
    private long _diskHits;
    private long _misses;
    private long _evictions;
    
    public CacheService(ILogger<CacheService> logger, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _exceptionHandler = exceptionHandler;
        
        // 默认配置
        _defaultExpiry = TimeSpan.FromHours(1);
        _maxMemoryCacheSize = 100 * 1024 * 1024; // 100MB
        _diskCacheDirectory = Path.Combine(Path.GetTempPath(), "DLT_CP_Cache");
        
        // 确保缓存目录存在
        Directory.CreateDirectory(_diskCacheDirectory);
        
        // 启动清理定时器
        _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("缓存服务已启动 - 内存限制: {MemoryLimit}MB, 磁盘目录: {DiskDirectory}", 
            _maxMemoryCacheSize / (1024 * 1024), _diskCacheDirectory);
    }
    
    /// <summary>
    /// 获取缓存项
    /// </summary>
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            // 首先检查内存缓存
            if (_memoryCache.TryGetValue(key, out var memoryItem) && !memoryItem.IsExpired)
            {
                Interlocked.Increment(ref _memoryHits);
                _logger.LogDebug("内存缓存命中 - Key: {Key}", key);
                
                // 更新访问时间
                memoryItem.LastAccessed = DateTime.Now;
                
                return JsonSerializer.Deserialize<T>(memoryItem.Data);
            }
            
            // 检查磁盘缓存
            var diskCacheFile = GetDiskCacheFilePath(key);
            if (File.Exists(diskCacheFile))
            {
                var diskItem = await LoadFromDiskAsync(diskCacheFile, cancellationToken);
                if (diskItem != null && !diskItem.IsExpired)
                {
                    Interlocked.Increment(ref _diskHits);
                    _logger.LogDebug("磁盘缓存命中 - Key: {Key}", key);
                    
                    // 将热数据提升到内存缓存
                    await SetMemoryCacheAsync(key, diskItem.Data, diskItem.ExpiresAt - DateTime.Now);
                    
                    return JsonSerializer.Deserialize<T>(diskItem.Data);
                }
                else if (diskItem?.IsExpired == true)
                {
                    // 删除过期的磁盘缓存
                    File.Delete(diskCacheFile);
                }
            }
            
            Interlocked.Increment(ref _misses);
            _logger.LogDebug("缓存未命中 - Key: {Key}", key);
            return null;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取缓存时发生错误 - Key: {Key}", key);
            _exceptionHandler?.HandleException(ex, $"获取缓存失败: {key}");
            return null;
        }
    }
    
    /// <summary>
    /// 设置缓存项
    /// </summary>
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            if (value == null)
            {
                await RemoveAsync(key, cancellationToken);
                return;
            }
            
            var actualExpiry = expiry ?? _defaultExpiry;
            var serializedData = JsonSerializer.Serialize(value);
            var dataSize = System.Text.Encoding.UTF8.GetByteCount(serializedData);
            
            // 设置内存缓存
            await SetMemoryCacheAsync(key, serializedData, actualExpiry);
            
            // 如果数据较大或重要，同时设置磁盘缓存
            if (dataSize > 1024 || actualExpiry > TimeSpan.FromMinutes(30))
            {
                await SetDiskCacheAsync(key, serializedData, actualExpiry, cancellationToken);
            }
            
            _logger.LogDebug("缓存已设置 - Key: {Key}, Size: {Size}bytes, Expiry: {Expiry}", 
                key, dataSize, actualExpiry);
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "设置缓存时发生错误 - Key: {Key}", key);
            _exceptionHandler?.HandleException(ex, $"设置缓存失败: {key}");
        }
    }
    
    /// <summary>
    /// 移除缓存项
    /// </summary>
    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            // 移除内存缓存
            _memoryCache.TryRemove(key, out _);
            
            // 移除磁盘缓存
            var diskCacheFile = GetDiskCacheFilePath(key);
            if (File.Exists(diskCacheFile))
            {
                File.Delete(diskCacheFile);
            }
            
            _logger.LogDebug("缓存已移除 - Key: {Key}", key);
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "移除缓存时发生错误 - Key: {Key}", key);
        }
    }
    
    /// <summary>
    /// 检查缓存项是否存在
    /// </summary>
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            // 检查内存缓存
            if (_memoryCache.TryGetValue(key, out var memoryItem) && !memoryItem.IsExpired)
            {
                return true;
            }
            
            // 检查磁盘缓存
            var diskCacheFile = GetDiskCacheFilePath(key);
            if (File.Exists(diskCacheFile))
            {
                var diskItem = await LoadFromDiskAsync(diskCacheFile, cancellationToken);
                return diskItem != null && !diskItem.IsExpired;
            }
            
            return false;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "检查缓存存在性时发生错误 - Key: {Key}", key);
            return false;
        }
    }
    
    /// <summary>
    /// 清空所有缓存
    /// </summary>
    public async Task ClearAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var memoryCount = _memoryCache.Count;
            
            // 清空内存缓存
            _memoryCache.Clear();
            
            // 清空磁盘缓存
            var diskFiles = Directory.GetFiles(_diskCacheDirectory, "*.cache");
            foreach (var file in diskFiles)
            {
                cancellationToken.ThrowIfCancellationRequested();
                File.Delete(file);
            }
            
            // 重置统计信息
            Interlocked.Exchange(ref _memoryHits, 0);
            Interlocked.Exchange(ref _diskHits, 0);
            Interlocked.Exchange(ref _misses, 0);
            Interlocked.Exchange(ref _evictions, 0);
            
            _logger.LogInformation("缓存已清空 - 内存项: {MemoryCount}, 磁盘文件: {DiskCount}", 
                memoryCount, diskFiles.Length);
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空缓存时发生错误");
            _exceptionHandler?.HandleException(ex, "清空缓存失败");
        }
    }
    
    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetStatistics()
    {
        var memorySize = _memoryCache.Values.Sum(item => System.Text.Encoding.UTF8.GetByteCount(item.Data));
        var diskFiles = Directory.Exists(_diskCacheDirectory) ? Directory.GetFiles(_diskCacheDirectory, "*.cache") : Array.Empty<string>();
        var diskSize = diskFiles.Sum(file => new FileInfo(file).Length);
        
        var totalRequests = _memoryHits + _diskHits + _misses;
        var hitRate = totalRequests > 0 ? (double)(_memoryHits + _diskHits) / totalRequests : 0;
        
        return new CacheStatistics
        {
            MemoryItems = _memoryCache.Count,
            MemorySize = memorySize,
            DiskItems = diskFiles.Length,
            DiskSize = diskSize,
            MemoryHits = _memoryHits,
            DiskHits = _diskHits,
            Misses = _misses,
            Evictions = _evictions,
            HitRate = hitRate,
            TotalRequests = totalRequests
        };
    }
    
    /// <summary>
    /// 生成缓存报告
    /// </summary>
    public string GenerateCacheReport()
    {
        try
        {
            var stats = GetStatistics();
            
            var report = new List<string>
            {
                "=== 缓存服务报告 ===",
                $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                "",
                "=== 缓存统计 ===",
                $"内存缓存项: {stats.MemoryItems}",
                $"内存使用: {stats.MemorySize / (1024.0 * 1024):F2}MB",
                $"磁盘缓存项: {stats.DiskItems}",
                $"磁盘使用: {stats.DiskSize / (1024.0 * 1024):F2}MB",
                "",
                "=== 性能统计 ===",
                $"总请求数: {stats.TotalRequests}",
                $"内存命中: {stats.MemoryHits} ({(stats.TotalRequests > 0 ? (double)stats.MemoryHits / stats.TotalRequests * 100 : 0):F1}%)",
                $"磁盘命中: {stats.DiskHits} ({(stats.TotalRequests > 0 ? (double)stats.DiskHits / stats.TotalRequests * 100 : 0):F1}%)",
                $"缓存未命中: {stats.Misses} ({(stats.TotalRequests > 0 ? (double)stats.Misses / stats.TotalRequests * 100 : 0):F1}%)",
                $"总命中率: {stats.HitRate * 100:F1}%",
                $"缓存驱逐: {stats.Evictions}",
                "",
                "=== 配置信息 ===",
                $"默认过期时间: {_defaultExpiry}",
                $"最大内存限制: {_maxMemoryCacheSize / (1024 * 1024)}MB",
                $"磁盘缓存目录: {_diskCacheDirectory}"
            };
            
            return string.Join(Environment.NewLine, report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成缓存报告时发生错误");
            return $"生成缓存报告失败: {ex.Message}";
        }
    }
    
    private async Task SetMemoryCacheAsync(string key, string data, TimeSpan expiry)
    {
        var cacheItem = new CacheItem
        {
            Key = key,
            Data = data,
            CreatedAt = DateTime.Now,
            ExpiresAt = DateTime.Now + expiry,
            LastAccessed = DateTime.Now
        };
        
        _memoryCache.AddOrUpdate(key, cacheItem, (k, existing) => cacheItem);
        
        // 检查内存使用限制
        await EnforceMemoryLimitAsync();
    }
    
    private async Task SetDiskCacheAsync(string key, string data, TimeSpan expiry, CancellationToken cancellationToken)
    {
        var cacheItem = new CacheItem
        {
            Key = key,
            Data = data,
            CreatedAt = DateTime.Now,
            ExpiresAt = DateTime.Now + expiry,
            LastAccessed = DateTime.Now
        };
        
        var diskCacheFile = GetDiskCacheFilePath(key);
        var serializedItem = JsonSerializer.Serialize(cacheItem);
        
        await File.WriteAllTextAsync(diskCacheFile, serializedItem, cancellationToken);
    }
    
    private async Task<CacheItem?> LoadFromDiskAsync(string filePath, CancellationToken cancellationToken)
    {
        try
        {
            var content = await File.ReadAllTextAsync(filePath, cancellationToken);
            return JsonSerializer.Deserialize<CacheItem>(content);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "加载磁盘缓存失败 - File: {FilePath}", filePath);
            return null;
        }
    }
    
    private string GetDiskCacheFilePath(string key)
    {
        var safeKey = string.Join("", key.Where(c => char.IsLetterOrDigit(c) || c == '_' || c == '-'));
        var hash = key.GetHashCode().ToString("X8");
        return Path.Combine(_diskCacheDirectory, $"{safeKey}_{hash}.cache");
    }
    
    private async Task EnforceMemoryLimitAsync()
    {
        var currentSize = _memoryCache.Values.Sum(item => System.Text.Encoding.UTF8.GetByteCount(item.Data));
        
        if (currentSize <= _maxMemoryCacheSize)
            return;
        
        // 按最后访问时间排序，移除最旧的项
        var itemsToRemove = _memoryCache.Values
            .OrderBy(item => item.LastAccessed)
            .Take(_memoryCache.Count / 4) // 移除25%的项
            .ToList();
        
        foreach (var item in itemsToRemove)
        {
            _memoryCache.TryRemove(item.Key, out _);
            Interlocked.Increment(ref _evictions);
        }
        
        _logger.LogDebug("内存缓存清理完成 - 移除项: {RemovedCount}, 当前大小: {CurrentSize}MB", 
            itemsToRemove.Count, currentSize / (1024.0 * 1024));
    }
    
    private void CleanupExpiredItems(object? state)
    {
        try
        {
            var expiredMemoryItems = 0;
            var expiredDiskFiles = 0;
            
            // 清理过期的内存缓存
            var expiredKeys = _memoryCache.Where(kvp => kvp.Value.IsExpired).Select(kvp => kvp.Key).ToList();
            foreach (var key in expiredKeys)
            {
                _memoryCache.TryRemove(key, out _);
                expiredMemoryItems++;
            }
            
            // 清理过期的磁盘缓存
            if (Directory.Exists(_diskCacheDirectory))
            {
                var diskFiles = Directory.GetFiles(_diskCacheDirectory, "*.cache");
                foreach (var file in diskFiles)
                {
                    try
                    {
                        var content = File.ReadAllText(file);
                        var item = JsonSerializer.Deserialize<CacheItem>(content);
                        if (item?.IsExpired == true)
                        {
                            File.Delete(file);
                            expiredDiskFiles++;
                        }
                    }
                    catch
                    {
                        // 如果文件损坏，直接删除
                        File.Delete(file);
                        expiredDiskFiles++;
                    }
                }
            }
            
            if (expiredMemoryItems > 0 || expiredDiskFiles > 0)
            {
                _logger.LogDebug("缓存清理完成 - 内存项: {MemoryItems}, 磁盘文件: {DiskFiles}", 
                    expiredMemoryItems, expiredDiskFiles);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理过期缓存时发生错误");
        }
    }
    
    public void Dispose()
    {
        try
        {
            _cleanupTimer?.Dispose();
            
            var stats = GetStatistics();
            _logger.LogInformation("缓存服务已释放 - 内存项: {MemoryItems}, 磁盘项: {DiskItems}, 命中率: {HitRate:F1}%", 
                stats.MemoryItems, stats.DiskItems, stats.HitRate * 100);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放缓存服务时发生错误");
        }
    }
}

/// <summary>
/// 缓存项
/// </summary>
public class CacheItem
{
    public string Key { get; set; } = string.Empty;
    public string Data { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime LastAccessed { get; set; }
    
    public bool IsExpired => DateTime.Now > ExpiresAt;
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    public int MemoryItems { get; set; }
    public long MemorySize { get; set; }
    public int DiskItems { get; set; }
    public long DiskSize { get; set; }
    public long MemoryHits { get; set; }
    public long DiskHits { get; set; }
    public long Misses { get; set; }
    public long Evictions { get; set; }
    public double HitRate { get; set; }
    public long TotalRequests { get; set; }
}