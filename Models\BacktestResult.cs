namespace DLT_CP.Models;

/// <summary>
/// 回测结果模型
/// </summary>
public class BacktestResult
{
    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;
    
    /// <summary>
    /// 回测开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 回测结束时间
    /// </summary>
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// 回测期数
    /// </summary>
    public int TotalPeriods { get; set; }
    
    /// <summary>
    /// 红球命中统计 [0中, 1中, 2中, 3中, 4中, 5中]
    /// </summary>
    public int[] RedHitCounts { get; set; } = new int[6];
    
    /// <summary>
    /// 蓝球命中统计 [0中, 1中, 2中]
    /// </summary>
    public int[] BlueHitCounts { get; set; } = new int[3];
    
    /// <summary>
    /// 平均红球命中数
    /// </summary>
    public double AverageRedHits => TotalPeriods > 0 ? 
        RedHitCounts.Select((count, index) => count * index).Sum() / (double)TotalPeriods : 0;
    
    /// <summary>
    /// 平均蓝球命中数
    /// </summary>
    public double AverageBlueHits => TotalPeriods > 0 ? 
        BlueHitCounts.Select((count, index) => count * index).Sum() / (double)TotalPeriods : 0;
    
    /// <summary>
    /// 红球命中率 (至少命中1个)
    /// </summary>
    public double RedHitRate => TotalPeriods > 0 ? 
        (TotalPeriods - RedHitCounts[0]) / (double)TotalPeriods : 0;
    
    /// <summary>
    /// 蓝球命中率 (至少命中1个)
    /// </summary>
    public double BlueHitRate => TotalPeriods > 0 ? 
        (TotalPeriods - BlueHitCounts[0]) / (double)TotalPeriods : 0;
    
    /// <summary>
    /// 综合评分 (0-100)
    /// </summary>
    public double OverallScore
    {
        get
        {
            if (TotalPeriods == 0) return 0;
            
            // 红球权重70%，蓝球权重30%
            var redScore = AverageRedHits / 5.0 * 70;
            var blueScore = AverageBlueHits / 2.0 * 30;
            
            return redScore + blueScore;
        }
    }
    
    /// <summary>
    /// 最佳命中记录
    /// </summary>
    public (int RedHits, int BlueHits, string Period) BestHit { get; set; }
    
    /// <summary>
    /// 预测准确度等级
    /// </summary>
    public string AccuracyLevel
    {
        get
        {
            return OverallScore switch
            {
                >= 80 => "优秀",
                >= 60 => "良好",
                >= 40 => "一般",
                >= 20 => "较差",
                _ => "很差"
            };
        }
    }
    
    /// <summary>
    /// 详细的回测记录
    /// </summary>
    public List<BacktestRecord> Records { get; set; } = new();
    
    /// <summary>
    /// 添加回测记录
    /// </summary>
    public void AddRecord(string period, PredictionResult prediction, LotteryDataRow actual)
    {
        var (redHits, blueHits) = prediction.CalculateMatches(actual);
        
        var record = new BacktestRecord
        {
            Period = period,
            PredictedRed = prediction.RedBalls,
            PredictedBlue = prediction.BlueBalls,
            ActualRed = actual.RedBalls,
            ActualBlue = actual.BlueBalls,
            RedHits = redHits,
            BlueHits = blueHits,
            Confidence = prediction.Confidence
        };
        
        Records.Add(record);
        
        // 更新统计
        RedHitCounts[redHits]++;
        BlueHitCounts[blueHits]++;
        TotalPeriods++;
        
        // 更新最佳命中记录
        if (redHits + blueHits > BestHit.RedHits + BestHit.BlueHits)
        {
            BestHit = (redHits, blueHits, period);
        }
    }
}

/// <summary>
/// 单次回测记录
/// </summary>
public class BacktestRecord
{
    public string Period { get; set; } = string.Empty;
    public int[] PredictedRed { get; set; } = Array.Empty<int>();
    public int[] PredictedBlue { get; set; } = Array.Empty<int>();
    public int[] ActualRed { get; set; } = Array.Empty<int>();
    public int[] ActualBlue { get; set; } = Array.Empty<int>();
    public int RedHits { get; set; }
    public int BlueHits { get; set; }
    public double Confidence { get; set; }
    
    public string PredictedRedString => string.Join(", ", PredictedRed.OrderBy(x => x));
    public string PredictedBlueString => string.Join(", ", PredictedBlue.OrderBy(x => x));
    public string ActualRedString => string.Join(", ", ActualRed.OrderBy(x => x));
    public string ActualBlueString => string.Join(", ", ActualBlue.OrderBy(x => x));
}