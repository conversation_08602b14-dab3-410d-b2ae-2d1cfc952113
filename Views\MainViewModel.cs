using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using DLT_CP.Commands;
using DLT_CP.Models;
using DLT_CP.Services;
using DLT_CP.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace DLT_CP.Views
{
    /// <summary>
    /// 主窗口的视图模型
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataService _dataService;
        private readonly IBacktestFramework _backtestFramework;

        #region 私有字段
        private string _selectedPredictor = "FrequencyAnalyzer";
        private string _issueNumber;
        private bool _isTraining;
        private bool _isPredicting;
        private bool _isBacktesting;
        private string _statusMessage = "就绪";
        private double _progressValue;
        private PredictionResult _currentPrediction;
        private BacktestResult _backtestResult;
        #endregion

        #region 公共属性
        /// <summary>
        /// 选中的预测器
        /// </summary>
        public string SelectedPredictor
        {
            get => _selectedPredictor;
            set => SetProperty(ref _selectedPredictor, value);
        }

        /// <summary>
        /// 期号输入
        /// </summary>
        public string IssueNumber
        {
            get => _issueNumber;
            set => SetProperty(ref _issueNumber, value);
        }

        /// <summary>
        /// 是否正在训练
        /// </summary>
        public bool IsTraining
        {
            get => _isTraining;
            set => SetProperty(ref _isTraining, value);
        }

        /// <summary>
        /// 是否正在预测
        /// </summary>
        public bool IsPredicting
        {
            get => _isPredicting;
            set => SetProperty(ref _isPredicting, value);
        }

        /// <summary>
        /// 是否正在回测
        /// </summary>
        public bool IsBacktesting
        {
            get => _isBacktesting;
            set => SetProperty(ref _isBacktesting, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 进度值
        /// </summary>
        public double ProgressValue
        {
            get => _progressValue;
            set => SetProperty(ref _progressValue, value);
        }

        /// <summary>
        /// 当前预测结果
        /// </summary>
        public PredictionResult CurrentPrediction
        {
            get => _currentPrediction;
            set => SetProperty(ref _currentPrediction, value);
        }

        /// <summary>
        /// 回测结果
        /// </summary>
        public BacktestResult BacktestResult
        {
            get => _backtestResult;
            set => SetProperty(ref _backtestResult, value);
        }

        /// <summary>
        /// 历史数据集合
        /// </summary>
        public ObservableCollection<LotteryDataRow> HistoryData { get; } = new ObservableCollection<LotteryDataRow>();

        /// <summary>
        /// 预测历史集合
        /// </summary>
        public ObservableCollection<PredictionResult> PredictionHistory { get; } = new ObservableCollection<PredictionResult>();

        /// <summary>
        /// 回测结果集合
        /// </summary>
        public ObservableCollection<BacktestResult> BacktestResults { get; } = new ObservableCollection<BacktestResult>();

        /// <summary>
        /// 可用的预测器列表
        /// </summary>
        public ObservableCollection<string> AvailablePredictors { get; } = new ObservableCollection<string>
        {
            "FrequencyAnalyzer",
            "MarkovChainPredictor",
            "MLNetPredictor",
            "TorchSharpPredictor"
        };
        #endregion

        #region 命令
        /// <summary>
        /// 加载数据命令
        /// </summary>
        public ICommand LoadDataCommand { get; }

        /// <summary>
        /// 训练模型命令
        /// </summary>
        public ICommand TrainModelCommand { get; }

        /// <summary>
        /// 预测命令
        /// </summary>
        public ICommand PredictCommand { get; }

        /// <summary>
        /// 运行回测命令
        /// </summary>
        public ICommand RunBacktestCommand { get; }

        /// <summary>
        /// 导出数据命令
        /// </summary>
        public ICommand ExportDataCommand { get; }

        /// <summary>
        /// 清空历史命令
        /// </summary>
        public ICommand ClearHistoryCommand { get; }
        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainViewModel(IServiceProvider serviceProvider, IDataService dataService, IBacktestFramework backtestFramework)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _backtestFramework = backtestFramework ?? throw new ArgumentNullException(nameof(backtestFramework));

            // 初始化命令
            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync(), () => !IsTraining && !IsPredicting && !IsBacktesting);
            TrainModelCommand = new RelayCommand(async () => await TrainModelAsync(), () => !IsTraining && !IsPredicting && !IsBacktesting && HistoryData.Count > 0);
            PredictCommand = new RelayCommand(async () => await PredictAsync(), () => !IsTraining && !IsPredicting && !IsBacktesting && !string.IsNullOrWhiteSpace(IssueNumber));
            RunBacktestCommand = new RelayCommand(async () => await RunBacktestAsync(), () => !IsTraining && !IsPredicting && !IsBacktesting && HistoryData.Count > 0);
            ExportDataCommand = new RelayCommand(async () => await ExportDataAsync(), () => PredictionHistory.Count > 0 || BacktestResults.Count > 0);
            ClearHistoryCommand = new RelayCommand(ClearHistory, () => PredictionHistory.Count > 0 || BacktestResults.Count > 0);

            // 初始化
            InitializeAsync();
        }

        #region 私有方法
        /// <summary>
        /// 初始化
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                StatusMessage = "正在初始化...";
                await LoadDataAsync();
                
                // 生成当前期号
                IssueNumber = GenerateCurrentIssueNumber();
                
                StatusMessage = "初始化完成";
            }
            catch (Exception ex)
            {
                StatusMessage = $"初始化失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                StatusMessage = "正在加载历史数据...";
                ProgressValue = 0;

                var data = await _dataService.LoadDataAsync();
                
                HistoryData.Clear();
                foreach (var item in data.OrderByDescending(x => x.IssueNumber))
                {
                    HistoryData.Add(item);
                }

                ProgressValue = 100;
                StatusMessage = $"已加载 {HistoryData.Count} 条历史数据";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载数据失败: {ex.Message}";
            }
            finally
            {
                ProgressValue = 0;
            }
        }

        /// <summary>
        /// 训练模型
        /// </summary>
        private async Task TrainModelAsync()
        {
            try
            {
                IsTraining = true;
                StatusMessage = "正在训练模型...";
                ProgressValue = 0;

                var predictor = GetCurrentPredictor();
                if (predictor == null)
                {
                    StatusMessage = "获取预测器失败";
                    return;
                }

                var trainingData = HistoryData.ToList();
                
                // 模拟训练进度
                var progress = new Progress<double>(value => ProgressValue = value);
                
                await Task.Run(() => predictor.TrainAsync(trainingData, progress));

                StatusMessage = $"模型训练完成 - {predictor.Name}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"训练失败: {ex.Message}";
            }
            finally
            {
                IsTraining = false;
                ProgressValue = 0;
            }
        }

        /// <summary>
        /// 预测
        /// </summary>
        private async Task PredictAsync()
        {
            try
            {
                IsPredicting = true;
                StatusMessage = "正在预测...";
                ProgressValue = 50;

                var predictor = GetCurrentPredictor();
                if (predictor == null)
                {
                    StatusMessage = "获取预测器失败";
                    return;
                }

                if (!predictor.IsTrained)
                {
                    StatusMessage = "模型未训练，请先训练模型";
                    return;
                }

                var result = await Task.Run(() => predictor.PredictAsync(IssueNumber));
                
                CurrentPrediction = result;
                PredictionHistory.Insert(0, result);

                ProgressValue = 100;
                StatusMessage = $"预测完成 - 期号: {result.IssueNumber}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"预测失败: {ex.Message}";
            }
            finally
            {
                IsPredicting = false;
                ProgressValue = 0;
            }
        }

        /// <summary>
        /// 运行回测
        /// </summary>
        private async Task RunBacktestAsync()
        {
            try
            {
                IsBacktesting = true;
                StatusMessage = "正在运行回测...";
                ProgressValue = 0;

                var predictor = GetCurrentPredictor();
                if (predictor == null)
                {
                    StatusMessage = "获取预测器失败";
                    return;
                }

                var testData = HistoryData.Take(100).ToList(); // 使用最近100期数据进行回测
                var trainingData = HistoryData.Skip(100).ToList();

                var progress = new Progress<double>(value => ProgressValue = value);
                
                var result = await _backtestFramework.RunBacktestAsync(predictor, trainingData, testData, progress);
                
                BacktestResult = result;
                BacktestResults.Insert(0, result);

                StatusMessage = $"回测完成 - 准确度: {result.AccuracyLevel}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"回测失败: {ex.Message}";
            }
            finally
            {
                IsBacktesting = false;
                ProgressValue = 0;
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        private async Task ExportDataAsync()
        {
            try
            {
                StatusMessage = "正在导出数据...";
                
                var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "DLT_Export");
                Directory.CreateDirectory(exportPath);

                // 导出预测历史
                if (PredictionHistory.Count > 0)
                {
                    var predictionFile = Path.Combine(exportPath, $"predictions_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                    await _dataService.ExportDataAsync(PredictionHistory.ToList(), predictionFile, ExportFormat.Json);
                }

                // 导出回测结果
                if (BacktestResults.Count > 0)
                {
                    var backtestFile = Path.Combine(exportPath, $"backtest_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                    await _dataService.ExportDataAsync(BacktestResults.ToList(), backtestFile, ExportFormat.Json);
                }

                StatusMessage = $"数据已导出到: {exportPath}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"导出失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 清空历史
        /// </summary>
        private void ClearHistory()
        {
            PredictionHistory.Clear();
            BacktestResults.Clear();
            CurrentPrediction = null;
            BacktestResult = null;
            StatusMessage = "历史记录已清空";
        }

        /// <summary>
        /// 生成当前期号
        /// </summary>
        private string GenerateCurrentIssueNumber()
        {
            var now = DateTime.Now;
            var year = now.Year % 100; // 取年份后两位
            
            // 简单的期号生成逻辑：年份 + 一年中的第几周
            var weekOfYear = System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
                now, System.Globalization.CalendarWeekRule.FirstDay, DayOfWeek.Monday);
            
            return $"{year:D2}{weekOfYear:D3}";
        }

        /// <summary>
        /// 获取当前预测器实例
        /// </summary>
        private IPredictor GetCurrentPredictor()
        {
            return SelectedPredictor switch
            {
                "FrequencyAnalyzer" => _serviceProvider.GetService<FrequencyAnalyzer>(),
                "MarkovChainPredictor" => _serviceProvider.GetService<MarkovChainPredictor>(),
                "MLNetPredictor" => _serviceProvider.GetService<MLNetPredictor>(),
                "TorchSharpPredictor" => _serviceProvider.GetService<TorchSharpPredictor>(),
                _ => null
            };
        }
        #endregion
    }
}