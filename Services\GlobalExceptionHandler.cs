using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Logging;
using DLT_CP.Exceptions;

namespace DLT_CP.Services;

/// <summary>
/// 全局异常处理服务
/// </summary>
public class GlobalExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;
    private readonly string _errorLogPath;
    
    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
        _errorLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "Errors");
        EnsureErrorLogDirectory();
    }
    
    /// <summary>
    /// 处理未捕获的异常
    /// </summary>
    public void HandleUnhandledException(Exception exception, bool isTerminating = false)
    {
        try
        {
            var errorId = Guid.NewGuid().ToString("N")[..8];
            var timestamp = DateTime.Now;
            
            // 记录到日志
            _logger.LogCritical(exception, 
                "[错误ID: {ErrorId}] 发生未处理的异常。终止应用: {IsTerminating}", 
                errorId, isTerminating);
            
            // 写入错误文件
            _ = Task.Run(() => WriteErrorToFileAsync(exception, errorId, timestamp, isTerminating));
            
            // 显示用户友好的错误消息
            ShowUserFriendlyError(exception, errorId, isTerminating);
        }
        catch (Exception ex)
        {
            // 防止异常处理器本身出错
            try
            {
                _logger.LogCritical(ex, "异常处理器本身发生错误");
            }
            catch
            {
                // 最后的防线，写入事件日志或文件
                File.AppendAllText(
                    Path.Combine(_errorLogPath, "critical_errors.log"),
                    $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 异常处理器错误: {ex}\n\n");
            }
        }
    }
    
    /// <summary>
    /// 处理业务异常
    /// </summary>
    public void HandleBusinessException(Exception exception, string context = "")
    {
        var errorId = Guid.NewGuid().ToString("N")[..8];
        
        switch (exception)
        {
            case PredictionException predEx:
                _logger.LogError(predEx, 
                    "[错误ID: {ErrorId}] 预测异常 - 预测器: {PredictorName}, 期号: {PeriodNumber}, 上下文: {Context}",
                    errorId, predEx.PredictorName, predEx.PeriodNumber, context);
                break;
                
            case DataProcessingException dataEx:
                _logger.LogError(dataEx, 
                    "[错误ID: {ErrorId}] 数据处理异常 - 文件: {FilePath}, 行号: {LineNumber}, 上下文: {Context}",
                    errorId, dataEx.FilePath, dataEx.LineNumber, context);
                break;
                
            case BacktestException backtestEx:
                _logger.LogError(backtestEx, 
                    "[错误ID: {ErrorId}] 回测异常 - 模型: {ModelName}, 期数: {PeriodIndex}, 上下文: {Context}",
                    errorId, backtestEx.ModelName, backtestEx.PeriodIndex, context);
                break;
                
            case ModelTrainingException trainingEx:
                _logger.LogError(trainingEx, 
                    "[错误ID: {ErrorId}] 模型训练异常 - 模型类型: {ModelType}, 轮次: {EpochNumber}, 上下文: {Context}",
                    errorId, trainingEx.ModelType, trainingEx.EpochNumber, context);
                break;
                
            default:
                _logger.LogError(exception, 
                    "[错误ID: {ErrorId}] 业务异常 - 类型: {ExceptionType}, 上下文: {Context}",
                    errorId, exception.GetType().Name, context);
                break;
        }
        
        // 异步写入详细错误信息
        _ = Task.Run(() => WriteErrorToFileAsync(exception, errorId, DateTime.Now, false, context));
    }
    
    /// <summary>
    /// 记录性能警告
    /// </summary>
    public void LogPerformanceWarning(string operation, TimeSpan duration, string details = "")
    {
        if (duration.TotalSeconds > 30) // 超过30秒记录警告
        {
            _logger.LogWarning(
                "性能警告 - 操作: {Operation}, 耗时: {Duration:F2}秒, 详情: {Details}",
                operation, duration.TotalSeconds, details);
        }
    }
    
    private void EnsureErrorLogDirectory()
    {
        try
        {
            if (!Directory.Exists(_errorLogPath))
            {
                Directory.CreateDirectory(_errorLogPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "无法创建错误日志目录: {Path}", _errorLogPath);
        }
    }
    
    private async Task WriteErrorToFileAsync(Exception exception, string errorId, DateTime timestamp, bool isTerminating, string context = "")
    {
        try
        {
            var fileName = $"error_{timestamp:yyyyMMdd}.log";
            var filePath = Path.Combine(_errorLogPath, fileName);
            
            var errorInfo = $"""
                ==================== 错误报告 ====================
                错误ID: {errorId}
                时间: {timestamp:yyyy-MM-dd HH:mm:ss.fff}
                应用终止: {isTerminating}
                上下文: {context}
                异常类型: {exception.GetType().FullName}
                错误消息: {exception.Message}
                堆栈跟踪:
                {exception.StackTrace}
                
                """;
            
            if (exception.InnerException != null)
            {
                errorInfo += $"""
                    内部异常类型: {exception.InnerException.GetType().FullName}
                    内部异常消息: {exception.InnerException.Message}
                    内部异常堆栈:
                    {exception.InnerException.StackTrace}
                    
                    """;
            }
            
            errorInfo += "================================================\n\n";
            
            await File.AppendAllTextAsync(filePath, errorInfo);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "写入错误文件失败");
        }
    }
    
    private void ShowUserFriendlyError(Exception exception, string errorId, bool isTerminating)
    {
        try
        {
            var message = exception switch
            {
                PredictionException => "预测过程中发生错误，请检查数据和模型配置。",
                DataProcessingException => "数据处理过程中发生错误，请检查数据文件格式。",
                BacktestException => "回测过程中发生错误，请检查回测参数设置。",
                ModelTrainingException => "模型训练过程中发生错误，请检查训练数据和参数。",
                FileNotFoundException => "找不到指定的文件，请检查文件路径。",
                UnauthorizedAccessException => "没有足够的权限访问文件或目录。",
                OutOfMemoryException => "内存不足，请关闭其他应用程序或减少数据量。",
                _ => "应用程序发生未知错误。"
            };
            
            var title = isTerminating ? "严重错误" : "操作错误";
            var fullMessage = $"{message}\n\n错误ID: {errorId}\n\n如果问题持续存在，请联系技术支持。";
            
            if (isTerminating)
            {
                fullMessage += "\n\n应用程序将关闭。";
            }
            
            Application.Current?.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(fullMessage, title, MessageBoxButton.OK, 
                    isTerminating ? MessageBoxImage.Error : MessageBoxImage.Warning);
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "显示用户错误消息失败");
        }
    }
}