<UserControl x:Class="DLT_CP.Views.PerformanceMonitorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2196F3" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="性能监控" FontSize="18" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                <Button Content="刷新" Margin="20,0,0,0" Padding="10,5" Command="{Binding RefreshCommand}"/>
                <Button Content="清空历史" Margin="10,0,0,0" Padding="10,5" Command="{Binding ClearHistoryCommand}"/>
                <Button Content="导出报告" Margin="10,0,0,0" Padding="10,5" Command="{Binding ExportReportCommand}"/>
            </StackPanel>
        </Border>
        
        <!-- 主要内容 -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- 实时监控 -->
            <TabItem Header="实时监控">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 关键指标卡片 -->
                    <UniformGrid Grid.Row="0" Rows="2" Columns="3" Margin="0,0,0,10">
                        <!-- CPU使用率 -->
                        <Border Background="#E3F2FD" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="CPU使用率" FontWeight="Bold" FontSize="14" Foreground="#1976D2"/>
                                <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F1}%}" FontSize="24" FontWeight="Bold" Foreground="#1976D2"/>
                                <ProgressBar Value="{Binding CpuUsage}" Maximum="100" Height="8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- 内存使用 -->
                        <Border Background="#E8F5E8" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="内存使用" FontWeight="Bold" FontSize="14" Foreground="#388E3C"/>
                                <TextBlock Text="{Binding MemoryUsageMB, StringFormat={}{0:F0} MB}" FontSize="24" FontWeight="Bold" Foreground="#388E3C"/>
                                <ProgressBar Value="{Binding MemoryUsagePercent}" Maximum="100" Height="8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- 活跃操作 -->
                        <Border Background="#FFF3E0" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="活跃操作" FontWeight="Bold" FontSize="14" Foreground="#F57C00"/>
                                <TextBlock Text="{Binding ActiveOperations}" FontSize="24" FontWeight="Bold" Foreground="#F57C00"/>
                                <TextBlock Text="个正在进行" FontSize="12" Foreground="#F57C00" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- 缓存命中率 -->
                        <Border Background="#F3E5F5" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="缓存命中率" FontWeight="Bold" FontSize="14" Foreground="#7B1FA2"/>
                                <TextBlock Text="{Binding CacheHitRate, StringFormat={}{0:F1}%}" FontSize="24" FontWeight="Bold" Foreground="#7B1FA2"/>
                                <ProgressBar Value="{Binding CacheHitRate}" Maximum="100" Height="8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- 平均响应时间 -->
                        <Border Background="#FFEBEE" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="平均响应时间" FontWeight="Bold" FontSize="14" Foreground="#C62828"/>
                                <TextBlock Text="{Binding AverageResponseTime, StringFormat={}{0:F0} ms}" FontSize="24" FontWeight="Bold" Foreground="#C62828"/>
                                <TextBlock Text="最近10次操作" FontSize="12" Foreground="#C62828" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- 错误率 -->
                        <Border Background="#FAFAFA" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="错误率" FontWeight="Bold" FontSize="14" Foreground="#424242"/>
                                <TextBlock Text="{Binding ErrorRate, StringFormat={}{0:F2}%}" FontSize="24" FontWeight="Bold" Foreground="#424242"/>
                                <TextBlock Text="最近1小时" FontSize="12" Foreground="#424242" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                    
                    <!-- 实时日志 -->
                    <GroupBox Grid.Row="1" Header="实时日志" Margin="0,10,0,0">
                        <ListBox ItemsSource="{Binding RecentLogs}" ScrollViewer.HorizontalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="5">
                                        <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" Width="60" Foreground="Gray"/>
                                        <TextBlock Text="{Binding Level}" Width="60" FontWeight="Bold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Level}" Value="ERROR">
                                                            <Setter Property="Foreground" Value="Red"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Level}" Value="WARN">
                                                            <Setter Property="Foreground" Value="Orange"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Level}" Value="INFO">
                                                            <Setter Property="Foreground" Value="Blue"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                        <TextBlock Text="{Binding Message}" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </GroupBox>
                </Grid>
            </TabItem>
            
            <!-- 性能历史 -->
            <TabItem Header="性能历史">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 时间范围选择 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="时间范围:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox SelectedItem="{Binding SelectedTimeRange}" Width="120">
                            <ComboBoxItem Content="最近1小时"/>
                            <ComboBoxItem Content="最近6小时"/>
                            <ComboBoxItem Content="最近24小时"/>
                            <ComboBoxItem Content="最近7天"/>
                        </ComboBox>
                        <Button Content="应用" Margin="10,0,0,0" Padding="10,5" Command="{Binding ApplyTimeRangeCommand}"/>
                    </StackPanel>
                    
                    <!-- 性能图表 -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding PerformanceHistory}" AutoGenerateColumns="False" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="时间" Binding="{Binding Timestamp, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTextColumn Header="操作" Binding="{Binding OperationName}" Width="120"/>
                            <DataGridTextColumn Header="耗时(ms)" Binding="{Binding Duration, StringFormat=F0}" Width="80"/>
                            <DataGridTextColumn Header="CPU(%)" Binding="{Binding CpuUsage, StringFormat=F1}" Width="70"/>
                            <DataGridTextColumn Header="内存(MB)" Binding="{Binding MemoryUsage, StringFormat=F0}" Width="80"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="详情" Binding="{Binding Details}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- 缓存统计 -->
            <TabItem Header="缓存统计">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 缓存概览 -->
                    <UniformGrid Grid.Row="0" Columns="4" Margin="0,0,0,10">
                        <Border Background="#E1F5FE" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="总缓存项" FontWeight="Bold" FontSize="14" Foreground="#0277BD"/>
                                <TextBlock Text="{Binding TotalCacheItems}" FontSize="20" FontWeight="Bold" Foreground="#0277BD"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="#E8F5E8" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="命中次数" FontWeight="Bold" FontSize="14" Foreground="#2E7D32"/>
                                <TextBlock Text="{Binding CacheHits}" FontSize="20" FontWeight="Bold" Foreground="#2E7D32"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="#FFEBEE" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="未命中次数" FontWeight="Bold" FontSize="14" Foreground="#C62828"/>
                                <TextBlock Text="{Binding CacheMisses}" FontSize="20" FontWeight="Bold" Foreground="#C62828"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="#FFF3E0" Margin="5" Padding="15" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="缓存大小" FontWeight="Bold" FontSize="14" Foreground="#EF6C00"/>
                                <TextBlock Text="{Binding CacheSizeMB, StringFormat={}{0:F1} MB}" FontSize="20" FontWeight="Bold" Foreground="#EF6C00"/>
                            </StackPanel>
                        </Border>
                    </UniformGrid>
                    
                    <!-- 缓存详情 -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding CacheDetails}" AutoGenerateColumns="False" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="缓存键" Binding="{Binding Key}" Width="200"/>
                            <DataGridTextColumn Header="大小(KB)" Binding="{Binding SizeKB, StringFormat=F1}" Width="80"/>
                            <DataGridTextColumn Header="命中次数" Binding="{Binding HitCount}" Width="80"/>
                            <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTextColumn Header="最后访问" Binding="{Binding LastAccessed, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                            <DataGridTextColumn Header="过期时间" Binding="{Binding ExpiresAt, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="最后更新:" Foreground="Gray"/>
                <TextBlock Text="{Binding LastUpdateTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Foreground="Gray" Margin="5,0,20,0"/>
                <TextBlock Text="自动刷新:" Foreground="Gray"/>
                <CheckBox IsChecked="{Binding AutoRefresh}" Margin="5,0,20,0"/>
                <TextBlock Text="刷新间隔:" Foreground="Gray"/>
                <ComboBox SelectedItem="{Binding RefreshInterval}" Width="80" Margin="5,0,0,0">
                    <ComboBoxItem Content="5秒"/>
                    <ComboBoxItem Content="10秒"/>
                    <ComboBoxItem Content="30秒"/>
                    <ComboBoxItem Content="60秒"/>
                </ComboBox>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>