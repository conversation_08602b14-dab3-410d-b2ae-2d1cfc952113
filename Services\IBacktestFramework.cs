using DLT_CP.Models;

namespace DLT_CP.Services;

/// <summary>
/// 回测框架接口
/// </summary>
public interface IBacktestFramework
{
    /// <summary>
    /// 执行回测
    /// </summary>
    /// <param name="predictor">预测器</param>
    /// <param name="data">历史数据</param>
    /// <param name="trainPeriods">训练期数</param>
    /// <param name="testPeriods">测试期数</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回测结果</returns>
    Task<BacktestResult> RunBacktestAsync(
        IPredictor predictor,
        IEnumerable<LotteryDataRow> data,
        int trainPeriods,
        int testPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 交叉验证回测
    /// </summary>
    /// <param name="predictor">预测器</param>
    /// <param name="data">历史数据</param>
    /// <param name="folds">折数</param>
    /// <param name="trainPeriods">每折训练期数</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>交叉验证结果</returns>
    Task<CrossValidationResult> RunCrossValidationAsync(
        IPredictor predictor,
        IEnumerable<LotteryDataRow> data,
        int folds,
        int trainPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 滚动窗口回测
    /// </summary>
    /// <param name="predictor">预测器</param>
    /// <param name="data">历史数据</param>
    /// <param name="windowSize">窗口大小</param>
    /// <param name="stepSize">步长</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>滚动窗口回测结果</returns>
    Task<RollingWindowResult> RunRollingWindowBacktestAsync(
        IPredictor predictor,
        IEnumerable<LotteryDataRow> data,
        int windowSize,
        int stepSize,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 比较多个预测器
    /// </summary>
    /// <param name="predictors">预测器列表</param>
    /// <param name="data">历史数据</param>
    /// <param name="trainPeriods">训练期数</param>
    /// <param name="testPeriods">测试期数</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>比较结果</returns>
    Task<ModelComparisonResult> CompareModelsAsync(
        IEnumerable<IPredictor> predictors,
        IEnumerable<LotteryDataRow> data,
        int trainPeriods,
        int testPeriods,
        IProgress<BacktestProgress>? progress = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 回测进度信息
/// </summary>
public class BacktestProgress
{
    public string CurrentTask { get; set; } = string.Empty;
    public int CurrentStep { get; set; }
    public int TotalSteps { get; set; }
    public double ProgressPercentage => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;
    public string? Message { get; set; }
}

/// <summary>
/// 交叉验证结果
/// </summary>
public class CrossValidationResult
{
    public List<BacktestResult> FoldResults { get; set; } = new();
    public double MeanScore { get; set; }
    public double StandardDeviation { get; set; }
    public string ModelName { get; set; } = string.Empty;
}

/// <summary>
/// 滚动窗口回测结果
/// </summary>
public class RollingWindowResult
{
    public List<BacktestResult> WindowResults { get; set; } = new();
    public double AverageScore { get; set; }
    public double ScoreStability { get; set; }
    public string ModelName { get; set; } = string.Empty;
}

/// <summary>
/// 模型比较结果
/// </summary>
public class ModelComparisonResult
{
    public List<BacktestResult> ModelResults { get; set; } = new();
    public string BestModelName { get; set; } = string.Empty;
    public Dictionary<string, double> ModelRankings { get; set; } = new();
    public DateTime ComparisonTime { get; set; }
}