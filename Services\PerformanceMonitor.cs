using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Collections.Concurrent;
using DLT_CP.Exceptions;

namespace DLT_CP.Services;

/// <summary>
/// 性能监控服务
/// </summary>
public class PerformanceMonitor : IDisposable
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly GlobalExceptionHandler? _exceptionHandler;
    private readonly Timer _performanceTimer;
    
    // 性能计数器
    private readonly ConcurrentDictionary<string, PerformanceCounter> _performanceCounters = new();
    private readonly ConcurrentDictionary<string, List<PerformanceMetric>> _performanceHistory = new();
    private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations = new();
    
    // 配置
    private readonly TimeSpan _monitoringInterval;
    private readonly int _maxHistoryEntries;
    private readonly TimeSpan _performanceWarningThreshold;
    
    public PerformanceMonitor(ILogger<PerformanceMonitor> logger, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _exceptionHandler = exceptionHandler;
        
        // 默认配置
        _monitoringInterval = TimeSpan.FromSeconds(30);
        _maxHistoryEntries = 1000;
        _performanceWarningThreshold = TimeSpan.FromSeconds(5);
        
        // 启动性能监控定时器
        _performanceTimer = new Timer(CollectPerformanceMetrics, null, _monitoringInterval, _monitoringInterval);
        
        _logger.LogInformation("性能监控器已启动 - 监控间隔: {Interval}秒, 警告阈值: {Threshold}秒", 
            _monitoringInterval.TotalSeconds, _performanceWarningThreshold.TotalSeconds);
    }
    
    /// <summary>
    /// 开始操作计时
    /// </summary>
    public string StartOperation(string operationName, string? context = null)
    {
        var operationId = $"{operationName}_{Guid.NewGuid():N}";
        var stopwatch = Stopwatch.StartNew();
        
        _activeOperations[operationId] = stopwatch;
        
        _logger.LogDebug("开始操作计时 - 操作: {OperationName}, ID: {OperationId}, 上下文: {Context}", 
            operationName, operationId, context ?? "无");
        
        return operationId;
    }
    
    /// <summary>
    /// 结束操作计时
    /// </summary>
    public TimeSpan EndOperation(string operationId, string? additionalInfo = null)
    {
        if (!_activeOperations.TryRemove(operationId, out var stopwatch))
        {
            _logger.LogWarning("未找到操作计时器 - ID: {OperationId}", operationId);
            return TimeSpan.Zero;
        }
        
        stopwatch.Stop();
        var duration = stopwatch.Elapsed;
        
        // 提取操作名称
        var operationName = operationId.Split('_')[0];
        
        // 记录性能指标
        RecordPerformanceMetric(operationName, duration, additionalInfo);
        
        _logger.LogDebug("操作计时结束 - 操作: {OperationName}, ID: {OperationId}, 耗时: {Duration}ms, 信息: {Info}", 
            operationName, operationId, duration.TotalMilliseconds, additionalInfo ?? "无");
        
        // 检查是否需要性能警告
        if (duration > _performanceWarningThreshold)
        {
            _exceptionHandler?.LogPerformanceWarning(
                operationName, 
                duration, 
                additionalInfo ?? "无额外信息");
        }
        
        return duration;
    }
    
    /// <summary>
    /// 记录性能指标
    /// </summary>
    public void RecordPerformanceMetric(string metricName, TimeSpan duration, string? additionalInfo = null)
    {
        var metric = new PerformanceMetric
        {
            MetricName = metricName,
            Duration = duration,
            Timestamp = DateTime.Now,
            AdditionalInfo = additionalInfo ?? string.Empty
        };
        
        _performanceHistory.AddOrUpdate(
            metricName,
            new List<PerformanceMetric> { metric },
            (key, existingList) =>
            {
                lock (existingList)
                {
                    existingList.Add(metric);
                    
                    // 保持历史记录在限制范围内
                    if (existingList.Count > _maxHistoryEntries)
                    {
                        existingList.RemoveRange(0, existingList.Count - _maxHistoryEntries);
                    }
                    
                    return existingList;
                }
            });
        
        // 更新性能计数器
        UpdatePerformanceCounter(metricName, duration);
    }
    
    /// <summary>
    /// 获取性能统计
    /// </summary>
    public PerformanceStatistics GetPerformanceStatistics(string metricName)
    {
        if (!_performanceHistory.TryGetValue(metricName, out var metrics))
        {
            return new PerformanceStatistics { MetricName = metricName };
        }
        
        lock (metrics)
        {
            if (metrics.Count == 0)
            {
                return new PerformanceStatistics { MetricName = metricName };
            }
            
            var durations = metrics.Select(m => m.Duration.TotalMilliseconds).ToList();
            
            return new PerformanceStatistics
            {
                MetricName = metricName,
                Count = metrics.Count,
                AverageDuration = TimeSpan.FromMilliseconds(durations.Average()),
                MinDuration = TimeSpan.FromMilliseconds(durations.Min()),
                MaxDuration = TimeSpan.FromMilliseconds(durations.Max()),
                MedianDuration = TimeSpan.FromMilliseconds(GetMedian(durations)),
                P95Duration = TimeSpan.FromMilliseconds(GetPercentile(durations, 0.95)),
                P99Duration = TimeSpan.FromMilliseconds(GetPercentile(durations, 0.99)),
                LastUpdated = metrics.Max(m => m.Timestamp)
            };
        }
    }
    
    /// <summary>
    /// 获取所有性能统计
    /// </summary>
    public Dictionary<string, PerformanceStatistics> GetAllPerformanceStatistics()
    {
        var result = new Dictionary<string, PerformanceStatistics>();
        
        foreach (var metricName in _performanceHistory.Keys)
        {
            result[metricName] = GetPerformanceStatistics(metricName);
        }
        
        return result;
    }
    
    /// <summary>
    /// 清理过期的性能数据
    /// </summary>
    public void CleanupExpiredData(TimeSpan expiry)
    {
        var cutoffTime = DateTime.Now - expiry;
        var cleanedMetrics = 0;
        
        foreach (var kvp in _performanceHistory.ToList())
        {
            var metricName = kvp.Key;
            var metrics = kvp.Value;
            
            lock (metrics)
            {
                var originalCount = metrics.Count;
                metrics.RemoveAll(m => m.Timestamp < cutoffTime);
                cleanedMetrics += originalCount - metrics.Count;
                
                // 如果没有剩余数据，移除整个条目
                if (metrics.Count == 0)
                {
                    _performanceHistory.TryRemove(metricName, out _);
                    _performanceCounters.TryRemove(metricName, out _);
                }
            }
        }
        
        if (cleanedMetrics > 0)
        {
            _logger.LogDebug("清理过期性能数据 - 清理条目: {CleanedCount}, 截止时间: {CutoffTime}", 
                cleanedMetrics, cutoffTime);
        }
    }
    
    /// <summary>
    /// 生成性能报告
    /// </summary>
    public string GeneratePerformanceReport(TimeSpan? timeRange = null)
    {
        try
        {
            var report = new List<string>
            {
                "=== 性能监控报告 ===",
                $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                $"监控间隔: {_monitoringInterval.TotalSeconds}秒",
                $"活跃操作: {_activeOperations.Count}",
                ""
            };
            
            var allStats = GetAllPerformanceStatistics();
            
            if (allStats.Any())
            {
                report.Add("=== 性能统计 ===");
                
                foreach (var stat in allStats.OrderByDescending(s => s.Value.Count))
                {
                    var stats = stat.Value;
                    report.Add($"\n操作: {stats.MetricName}");
                    report.Add($"  执行次数: {stats.Count}");
                    report.Add($"  平均耗时: {stats.AverageDuration.TotalMilliseconds:F2}ms");
                    report.Add($"  最小耗时: {stats.MinDuration.TotalMilliseconds:F2}ms");
                    report.Add($"  最大耗时: {stats.MaxDuration.TotalMilliseconds:F2}ms");
                    report.Add($"  中位数: {stats.MedianDuration.TotalMilliseconds:F2}ms");
                    report.Add($"  P95: {stats.P95Duration.TotalMilliseconds:F2}ms");
                    report.Add($"  P99: {stats.P99Duration.TotalMilliseconds:F2}ms");
                    report.Add($"  最后更新: {stats.LastUpdated:HH:mm:ss}");
                }
            }
            else
            {
                report.Add("无性能统计数据");
            }
            
            // 添加系统性能信息
            report.Add("\n=== 系统性能 ===");
            report.Add($"CPU使用率: {GetCpuUsage():F1}%");
            report.Add($"内存使用: {GC.GetTotalMemory(false) / (1024 * 1024):F1}MB");
            report.Add($"工作集: {Environment.WorkingSet / (1024 * 1024):F1}MB");
            
            return string.Join(Environment.NewLine, report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成性能报告时发生错误");
            return $"生成性能报告失败: {ex.Message}";
        }
    }
    
    /// <summary>
    /// 检测性能异常
    /// </summary>
    public List<PerformanceAnomaly> DetectPerformanceAnomalies()
    {
        var anomalies = new List<PerformanceAnomaly>();
        
        try
        {
            foreach (var kvp in _performanceHistory)
            {
                var metricName = kvp.Key;
                var stats = GetPerformanceStatistics(metricName);
                
                // 检测异常慢的操作
                if (stats.MaxDuration > _performanceWarningThreshold * 2)
                {
                    anomalies.Add(new PerformanceAnomaly
                    {
                        MetricName = metricName,
                        AnomalyType = PerformanceAnomalyType.SlowOperation,
                        Description = $"操作耗时异常: 最大耗时 {stats.MaxDuration.TotalSeconds:F2}秒",
                        Severity = PerformanceAnomalySeverity.High,
                        DetectedAt = DateTime.Now
                    });
                }
                
                // 检测性能退化
                if (stats.P95Duration > stats.AverageDuration * 3)
                {
                    anomalies.Add(new PerformanceAnomaly
                    {
                        MetricName = metricName,
                        AnomalyType = PerformanceAnomalyType.PerformanceDegradation,
                        Description = $"性能退化: P95耗时是平均值的{stats.P95Duration.TotalMilliseconds / stats.AverageDuration.TotalMilliseconds:F1}倍",
                        Severity = PerformanceAnomalySeverity.Medium,
                        DetectedAt = DateTime.Now
                    });
                }
            }
            
            // 检测内存泄漏迹象
            var currentMemory = GC.GetTotalMemory(false);
            if (currentMemory > 500 * 1024 * 1024) // 500MB
            {
                anomalies.Add(new PerformanceAnomaly
                {
                    MetricName = "系统内存",
                    AnomalyType = PerformanceAnomalyType.MemoryLeak,
                    Description = $"内存使用过高: {currentMemory / (1024 * 1024):F1}MB",
                    Severity = PerformanceAnomalySeverity.High,
                    DetectedAt = DateTime.Now
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检测性能异常时发生错误");
        }
        
        return anomalies;
    }
    
    private void UpdatePerformanceCounter(string metricName, TimeSpan duration)
    {
        _performanceCounters.AddOrUpdate(
            metricName,
            new PerformanceCounter { Count = 1, TotalDuration = duration },
            (key, existing) => new PerformanceCounter
            {
                Count = existing.Count + 1,
                TotalDuration = existing.TotalDuration + duration
            });
    }
    
    private void CollectPerformanceMetrics(object? state)
    {
        try
        {
            // 清理过期数据
            CleanupExpiredData(TimeSpan.FromHours(24));
            
            // 检测性能异常
            var anomalies = DetectPerformanceAnomalies();
            
            foreach (var anomaly in anomalies.Where(a => a.Severity == PerformanceAnomalySeverity.High))
            {
                _logger.LogWarning("检测到性能异常 - 指标: {MetricName}, 类型: {AnomalyType}, 描述: {Description}", 
                    anomaly.MetricName, anomaly.AnomalyType, anomaly.Description);
            }
            
            // 记录当前活跃操作数量
            if (_activeOperations.Count > 10)
            {
                _logger.LogWarning("活跃操作数量较多: {ActiveCount}", _activeOperations.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集性能指标时发生错误");
        }
    }
    
    private double GetMedian(List<double> values)
    {
        var sorted = values.OrderBy(x => x).ToList();
        var count = sorted.Count;
        
        if (count % 2 == 0)
        {
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        }
        else
        {
            return sorted[count / 2];
        }
    }
    
    private double GetPercentile(List<double> values, double percentile)
    {
        var sorted = values.OrderBy(x => x).ToList();
        var index = (int)Math.Ceiling(sorted.Count * percentile) - 1;
        return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
    }
    
    private double GetCpuUsage()
    {
        try
        {
            using var process = Process.GetCurrentProcess();
            return process.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
        }
        catch
        {
            return 0;
        }
    }
    
    public void Dispose()
    {
        try
        {
            _performanceTimer?.Dispose();
            
            // 清理活跃操作
            foreach (var operation in _activeOperations.Keys.ToList())
            {
                EndOperation(operation, "应用程序关闭");
            }
            
            _logger.LogInformation("性能监控器已释放 - 监控指标: {MetricCount}", _performanceHistory.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放性能监控器时发生错误");
        }
    }
}

/// <summary>
/// 性能计数器
/// </summary>
public class PerformanceCounter
{
    public int Count { get; set; }
    public TimeSpan TotalDuration { get; set; }
}

/// <summary>
/// 性能指标
/// </summary>
public class PerformanceMetric
{
    public string MetricName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public DateTime Timestamp { get; set; }
    public string AdditionalInfo { get; set; } = string.Empty;
}

/// <summary>
/// 性能统计
/// </summary>
public class PerformanceStatistics
{
    public string MetricName { get; set; } = string.Empty;
    public int Count { get; set; }
    public TimeSpan AverageDuration { get; set; }
    public TimeSpan MinDuration { get; set; }
    public TimeSpan MaxDuration { get; set; }
    public TimeSpan MedianDuration { get; set; }
    public TimeSpan P95Duration { get; set; }
    public TimeSpan P99Duration { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 性能异常
/// </summary>
public class PerformanceAnomaly
{
    public string MetricName { get; set; } = string.Empty;
    public PerformanceAnomalyType AnomalyType { get; set; }
    public string Description { get; set; } = string.Empty;
    public PerformanceAnomalySeverity Severity { get; set; }
    public DateTime DetectedAt { get; set; }
}

/// <summary>
/// 性能异常类型
/// </summary>
public enum PerformanceAnomalyType
{
    SlowOperation,
    PerformanceDegradation,
    MemoryLeak,
    HighCpuUsage
}

/// <summary>
/// 性能异常严重程度
/// </summary>
public enum PerformanceAnomalySeverity
{
    Low,
    Medium,
    High,
    Critical
}