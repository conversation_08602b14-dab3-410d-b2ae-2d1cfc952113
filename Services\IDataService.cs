using DLT_CP.Models;

namespace DLT_CP.Services;

/// <summary>
/// 数据服务接口
/// </summary>
public interface IDataService
{
    /// <summary>
    /// 加载历史数据
    /// </summary>
    /// <param name="filePath">数据文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>历史数据列表</returns>
    Task<IEnumerable<LotteryDataRow>> LoadDataAsync(string filePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 保存数据
    /// </summary>
    /// <param name="data">数据列表</param>
    /// <param name="filePath">保存路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SaveDataAsync(IEnumerable<LotteryDataRow> data, string filePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取最新数据
    /// </summary>
    /// <param name="count">获取数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最新数据</returns>
    Task<IEnumerable<LotteryDataRow>> GetLatestDataAsync(int count, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按期号范围获取数据
    /// </summary>
    /// <param name="startPeriod">开始期号</param>
    /// <param name="endPeriod">结束期号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>指定范围的数据</returns>
    Task<IEnumerable<LotteryDataRow>> GetDataByPeriodRangeAsync(string startPeriod, string endPeriod, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 按日期范围获取数据
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>指定日期范围的数据</returns>
    Task<IEnumerable<LotteryDataRow>> GetDataByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <param name="data">要验证的数据</param>
    /// <returns>验证结果</returns>
    DataValidationResult ValidateData(IEnumerable<LotteryDataRow> data);
    
    /// <summary>
    /// 清理和预处理数据
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>清理后的数据</returns>
    IEnumerable<LotteryDataRow> CleanData(IEnumerable<LotteryDataRow> data);
    
    /// <summary>
    /// 获取数据统计信息
    /// </summary>
    /// <param name="data">数据列表</param>
    /// <returns>统计信息</returns>
    DataStatistics GetDataStatistics(IEnumerable<LotteryDataRow> data);
    
    /// <summary>
    /// 导出数据到不同格式
    /// </summary>
    /// <param name="data">数据列表</param>
    /// <param name="filePath">导出路径</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task ExportDataAsync(IEnumerable<LotteryDataRow> data, string filePath, ExportFormat format, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 从网络更新数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的数据数量</returns>
    Task<int> UpdateDataFromWebAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据验证结果
/// </summary>
public class DataValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public int TotalRecords { get; set; }
    public int ValidRecords { get; set; }
    public int InvalidRecords => TotalRecords - ValidRecords;
}

/// <summary>
/// 数据统计信息
/// </summary>
public class DataStatistics
{
    public int TotalPeriods { get; set; }
    public DateTime EarliestDate { get; set; }
    public DateTime LatestDate { get; set; }
    public Dictionary<int, int> RedBallFrequency { get; set; } = new();
    public Dictionary<int, int> BlueBallFrequency { get; set; } = new();
    public Dictionary<string, int> OddEvenPatterns { get; set; } = new();
    public Dictionary<string, int> SizePatterns { get; set; } = new();
    public double AverageRedSum { get; set; }
    public double AverageBlueSum { get; set; }
}

/// <summary>
/// 导出格式
/// </summary>
public enum ExportFormat
{
    Csv,
    Json,
    Excel,
    Xml
}