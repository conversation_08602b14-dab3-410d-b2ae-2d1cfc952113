using CsvHelper;
using CsvHelper.Configuration;
using DLT_CP.Models;
using DLT_CP.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Globalization;
using System.Text.Json;
using System.Diagnostics;
using System.Collections.Concurrent;
using System.IO;

namespace DLT_CP.Services;

/// <summary>
/// 数据服务实现
/// </summary>
public class DataService : IDataService
{
    private readonly ILogger<DataService> _logger;
    private readonly LotteryConfig _config;
    private readonly GlobalExceptionHandler? _exceptionHandler;
    
    // 多级缓存策略
    private readonly ConcurrentDictionary<string, List<LotteryDataRow>> _dataCache = new();
    private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
    private readonly ConcurrentDictionary<string, object> _cacheLocks = new();
    
    private readonly List<LotteryDataRow> _cachedData = new();
    private DateTime _lastLoadTime = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);
    private readonly TimeSpan _fileCacheExpiry = TimeSpan.FromHours(1);
    
    public DataService(ILogger<DataService> logger, IOptions<LotteryConfig> config, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
        _exceptionHandler = exceptionHandler;
        
        ValidateConfiguration();
    }
    
    public async Task<IEnumerable<LotteryDataRow>> LoadDataAsync(CancellationToken cancellationToken = default)
    {
        return await LoadDataAsync(_config.DataPath, cancellationToken);
    }
    
    public async Task<IEnumerable<LotteryDataRow>> LoadDataAsync(string filePath, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var cacheKey = $"file_data_{filePath}";
        
        try
        {
            // 检查内存缓存
            if (TryGetFromCache(cacheKey, out var cachedData))
            {
                _logger.LogDebug("使用内存缓存数据 - 缓存键: {CacheKey}", cacheKey);
                return cachedData;
            }

            _logger.LogInformation("开始加载彩票数据 - 数据路径: {DataPath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new DataProcessingException(
                    $"数据文件不存在: {filePath}", 
                    "文件加载", 
                    new FileNotFoundException($"数据文件不存在: {filePath}"));
            }

            var fileInfo = new FileInfo(filePath);
            _logger.LogDebug("文件信息 - 大小: {Size} bytes, 修改时间: {ModifiedTime}", 
                fileInfo.Length, fileInfo.LastWriteTime);

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            List<LotteryDataRow> data;

            switch (extension)
            {
                case ".csv":
                    data = await LoadFromCsvAsync(filePath, cancellationToken);
                    break;
                case ".json":
                    data = await LoadFromJsonAsync(filePath, cancellationToken);
                    break;
                default:
                    throw new DataProcessingException(
                        $"不支持的文件格式: {extension}", 
                        "文件格式验证", 
                        new NotSupportedException($"不支持的文件格式: {extension}"));
            }

            // 数据验证和处理
            var validData = await ProcessAndValidateDataAsync(data, cancellationToken);

            // 更新缓存
            UpdateCache(cacheKey, validData);

            stopwatch.Stop();
            _logger.LogInformation("数据加载完成 - 记录数: {Count}, 耗时: {Duration}ms", 
                validData.Count, stopwatch.ElapsedMilliseconds);
            
            // 记录性能警告
            _exceptionHandler?.LogPerformanceWarning(
                "数据加载", 
                stopwatch.Elapsed, 
                $"文件: {Path.GetFileName(filePath)}, 记录数: {validData.Count}");
            
            return validData;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("数据加载被取消");
            throw;
        }
        catch (DataProcessingException)
        {
            throw; // 重新抛出业务异常
        }
        catch (Exception ex)
        {
            var dataEx = new DataProcessingException(
                $"加载数据时发生未预期错误: {ex.Message}", 
                "数据加载", 
                ex);
            
            _exceptionHandler?.HandleBusinessException(dataEx, "数据服务");
            throw dataEx;
        }
    }
    
    public async Task SaveDataAsync(IEnumerable<LotteryDataRow> data, string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始保存数据到文件: {FilePath}", filePath);
            
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            var extension = Path.GetExtension(filePath).ToLower();
            
            switch (extension)
            {
                case ".csv":
                    await SaveCsvDataAsync(data, filePath, cancellationToken);
                    break;
                case ".json":
                    await SaveJsonDataAsync(data, filePath, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"不支持的文件格式: {extension}");
            }
            
            _logger.LogInformation("成功保存 {Count} 条数据记录", data.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存数据文件时发生错误: {FilePath}", filePath);
            throw;
        }
    }
    
    public async Task<IEnumerable<LotteryDataRow>> GetLatestDataAsync(int count, CancellationToken cancellationToken = default)
    {
        if (IsCacheValid())
        {
            return _cachedData.OrderByDescending(x => x.DrawDate).Take(count);
        }
        
        // 如果缓存无效，需要重新加载数据
        _logger.LogWarning("缓存已过期，需要重新加载数据");
        return Enumerable.Empty<LotteryDataRow>();
    }
    
    public async Task<IEnumerable<LotteryDataRow>> GetDataByPeriodRangeAsync(string startPeriod, string endPeriod, CancellationToken cancellationToken = default)
    {
        if (!IsCacheValid())
        {
            _logger.LogWarning("缓存无效，无法按期号范围获取数据");
            return Enumerable.Empty<LotteryDataRow>();
        }
        
        return _cachedData.Where(x => 
            string.Compare(x.PeriodNumber, startPeriod, StringComparison.Ordinal) >= 0 &&
            string.Compare(x.PeriodNumber, endPeriod, StringComparison.Ordinal) <= 0)
            .OrderBy(x => x.PeriodNumber);
    }
    
    public async Task<IEnumerable<LotteryDataRow>> GetDataByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        if (!IsCacheValid())
        {
            _logger.LogWarning("缓存无效，无法按日期范围获取数据");
            return Enumerable.Empty<LotteryDataRow>();
        }
        
        return _cachedData.Where(x => x.DrawDate >= startDate && x.DrawDate <= endDate)
            .OrderBy(x => x.DrawDate);
    }
    
    public DataValidationResult ValidateData(IEnumerable<LotteryDataRow> data)
    {
        var result = new DataValidationResult();
        var dataList = data.ToList();
        result.TotalRecords = dataList.Count;
        
        foreach (var row in dataList)
        {
            var rowErrors = ValidateRow(row);
            if (rowErrors.Any())
            {
                result.Errors.AddRange(rowErrors.Select(e => $"期号 {row.PeriodNumber}: {e}"));
            }
            else
            {
                result.ValidRecords++;
            }
        }
        
        // 检查期号连续性
        var sortedData = dataList.OrderBy(x => x.PeriodNumber).ToList();
        for (int i = 1; i < sortedData.Count; i++)
        {
            var current = sortedData[i].PeriodNumber;
            var previous = sortedData[i - 1].PeriodNumber;
            
            if (!IsConsecutivePeriod(previous, current))
            {
                result.Warnings.Add($"期号不连续: {previous} -> {current}");
            }
        }
        
        // 检查日期连续性
        var sortedByDate = dataList.OrderBy(x => x.DrawDate).ToList();
        for (int i = 1; i < sortedByDate.Count; i++)
        {
            var daysDiff = (sortedByDate[i].DrawDate - sortedByDate[i - 1].DrawDate).Days;
            if (daysDiff > 7) // 大乐透通常每周开奖2-3次
            {
                result.Warnings.Add($"开奖日期间隔过长: {sortedByDate[i - 1].DrawDate:yyyy-MM-dd} -> {sortedByDate[i].DrawDate:yyyy-MM-dd}");
            }
        }
        
        result.IsValid = result.Errors.Count == 0;
        return result;
    }
    
    public IEnumerable<LotteryDataRow> CleanData(IEnumerable<LotteryDataRow> data)
    {
        var cleanedData = new List<LotteryDataRow>();
        
        foreach (var row in data)
        {
            if (ValidateRow(row).Count == 0)
            {
                // 数据有效，进行清理
                var cleanedRow = new LotteryDataRow
                {
                    PeriodNumber = row.PeriodNumber.Trim(),
                    DrawDate = row.DrawDate,
                    Red1 = row.Red1,
                    Red2 = row.Red2,
                    Red3 = row.Red3,
                    Red4 = row.Red4,
                    Red5 = row.Red5,
                    Blue1 = row.Blue1,
                    Blue2 = row.Blue2
                };
                
                cleanedData.Add(cleanedRow);
            }
        }
        
        // 去重（基于期号）
        var uniqueData = cleanedData.GroupBy(x => x.PeriodNumber)
            .Select(g => g.OrderByDescending(x => x.DrawDate).First())
            .OrderBy(x => x.PeriodNumber)
            .ToList();
        
        _logger.LogInformation("数据清理完成，原始记录: {Original}，清理后记录: {Cleaned}", 
            data.Count(), uniqueData.Count);
        
        return uniqueData;
    }
    
    public DataStatistics GetDataStatistics(IEnumerable<LotteryDataRow> data)
    {
        var dataList = data.ToList();
        var stats = new DataStatistics
        {
            TotalPeriods = dataList.Count,
            EarliestDate = dataList.Min(x => x.DrawDate),
            LatestDate = dataList.Max(x => x.DrawDate)
        };
        
        // 计算球号频率
        for (int i = 1; i <= 35; i++)
        {
            stats.RedBallFrequency[i] = dataList.Sum(x => x.RedBalls.Count(b => b == i));
        }
        
        for (int i = 1; i <= 12; i++)
        {
            stats.BlueBallFrequency[i] = dataList.Sum(x => x.BlueBalls.Count(b => b == i));
        }
        
        // 计算模式统计
        foreach (var row in dataList)
        {
            var redOddEven = row.RedOddEvenRatio;
            var redOddEvenKey = $"红球奇偶{redOddEven.Odd}:{redOddEven.Even}";
            stats.OddEvenPatterns[redOddEvenKey] = stats.OddEvenPatterns.GetValueOrDefault(redOddEvenKey, 0) + 1;
            
            var redSize = row.RedSizeRatio;
            var redSizeKey = $"红球大小{redSize.Large}:{redSize.Small}";
            stats.SizePatterns[redSizeKey] = stats.SizePatterns.GetValueOrDefault(redSizeKey, 0) + 1;
            
            var blueOddEven = row.BlueOddEvenRatio;
            var blueOddEvenKey = $"蓝球奇偶{blueOddEven.Odd}:{blueOddEven.Even}";
            stats.OddEvenPatterns[blueOddEvenKey] = stats.OddEvenPatterns.GetValueOrDefault(blueOddEvenKey, 0) + 1;
            
            var blueSize = row.BlueSizeRatio;
            var blueSizeKey = $"蓝球大小{blueSize.Large}:{blueSize.Small}";
            stats.SizePatterns[blueSizeKey] = stats.SizePatterns.GetValueOrDefault(blueSizeKey, 0) + 1;
        }
        
        // 计算平均和值
        stats.AverageRedSum = dataList.Average(x => x.RedBalls.Sum());
        stats.AverageBlueSum = dataList.Average(x => x.BlueBalls.Sum());
        
        return stats;
    }
    
    public async Task ExportDataAsync(IEnumerable<LotteryDataRow> data, string filePath, ExportFormat format, CancellationToken cancellationToken = default)
    {
        switch (format)
        {
            case ExportFormat.Csv:
                await SaveCsvDataAsync(data, filePath, cancellationToken);
                break;
            case ExportFormat.Json:
                await SaveJsonDataAsync(data, filePath, cancellationToken);
                break;
            default:
                throw new NotSupportedException($"不支持的导出格式: {format}");
        }
    }
    
    public Task<int> UpdateDataFromWebAsync(CancellationToken cancellationToken = default)
    {
        // 这里应该实现从网络获取最新数据的逻辑
        // 由于涉及具体的数据源，这里只是一个占位符实现
        _logger.LogInformation("网络数据更新功能待实现");
        return Task.FromResult(0);
    }
    
    private async Task<List<LotteryDataRow>> LoadFromCsvAsync(string filePath, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("开始从CSV文件加载数据 - 文件: {FilePath}", filePath);
            
            var content = await File.ReadAllTextAsync(filePath, cancellationToken);
            
            if (string.IsNullOrWhiteSpace(content))
            {
                throw new DataProcessingException(
                    "CSV文件内容为空", 
                    "CSV解析", 
                    new InvalidDataException("CSV文件内容为空"));
            }
            
            using var reader = new StringReader(content);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                BadDataFound = null,
                HeaderValidated = null
            });

            var records = new List<LotteryDataRow>();
            var lineNumber = 1; // 从1开始，因为有标题行
            
            try
            {
                await foreach (var record in csv.GetRecordsAsync<LotteryDataRow>(cancellationToken))
                {
                    lineNumber++;
                    
                    if (record != null)
                    {
                        records.Add(record);
                    }
                    
                    // 定期检查取消令牌
                    if (lineNumber % 1000 == 0)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        _logger.LogDebug("CSV加载进度 - 已处理: {ProcessedLines} 行", lineNumber);
                    }
                }
            }
            catch (CsvHelperException csvEx)
            {
                throw new DataProcessingException(
                    $"CSV解析错误 - 行号: {lineNumber}, 错误: {csvEx.Message}", 
                    "CSV解析", 
                    csvEx);
            }
            
            stopwatch.Stop();
            _logger.LogDebug("CSV文件加载完成 - 记录数: {Count}, 耗时: {Duration}ms", 
                records.Count, stopwatch.ElapsedMilliseconds);
            
            return records;
        }
        catch (Exception ex) when (!(ex is DataProcessingException))
        {
            throw new DataProcessingException(
                $"读取CSV文件时发生错误: {ex.Message}", 
                "CSV文件读取", 
                ex);
        }
    }
    
    private async Task<List<LotteryDataRow>> LoadFromJsonAsync(string filePath, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("开始从JSON文件加载数据 - 文件: {FilePath}", filePath);
            
            var jsonContent = await File.ReadAllTextAsync(filePath, cancellationToken);
            
            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                throw new DataProcessingException(
                    "JSON文件内容为空", 
                    "JSON解析", 
                    new InvalidDataException("JSON文件内容为空"));
            }
            
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true
            };
            
            var records = JsonSerializer.Deserialize<List<LotteryDataRow>>(jsonContent, options);
            
            if (records == null)
            {
                _logger.LogWarning("JSON反序列化结果为null，返回空列表");
                return new List<LotteryDataRow>();
            }
            
            stopwatch.Stop();
            _logger.LogDebug("JSON文件加载完成 - 记录数: {Count}, 耗时: {Duration}ms", 
                records.Count, stopwatch.ElapsedMilliseconds);
            
            return records;
        }
        catch (JsonException jsonEx)
        {
            throw new DataProcessingException(
                $"JSON解析错误: {jsonEx.Message}", 
                "JSON解析", 
                jsonEx);
        }
        catch (Exception ex) when (!(ex is DataProcessingException))
        {
            throw new DataProcessingException(
                $"读取JSON文件时发生错误: {ex.Message}", 
                "JSON文件读取", 
                ex);
        }
    }
    
    private async Task SaveCsvDataAsync(IEnumerable<LotteryDataRow> data, string filePath, CancellationToken cancellationToken)
    {
        using var writer = new StreamWriter(filePath);
        using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
        
        await csv.WriteRecordsAsync(data, cancellationToken);
    }
    
    private async Task SaveJsonDataAsync(IEnumerable<LotteryDataRow> data, string filePath, CancellationToken cancellationToken)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        
        var json = JsonSerializer.Serialize(data, options);
        await File.WriteAllTextAsync(filePath, json, cancellationToken);
    }
    

    
    private bool IsConsecutivePeriod(string previous, string current)
    {
        // 简单的期号连续性检查
        // 实际实现可能需要更复杂的逻辑来处理年份变化等情况
        if (int.TryParse(previous, out int prevNum) && int.TryParse(current, out int currNum))
        {
            return currNum == prevNum + 1;
        }
        
        return true; // 如果无法解析，假设是连续的
    }
    
    private bool IsCacheValid()
    {
        return _cachedData.Any() && DateTime.Now - _lastLoadTime < _cacheExpiry;
    }
    
    private void ValidateConfiguration()
    {
        if (string.IsNullOrWhiteSpace(_config.DataPath))
        {
            throw new ArgumentException("数据路径不能为空", nameof(_config.DataPath));
        }
        
        var directory = Path.GetDirectoryName(_config.DataPath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            _logger.LogWarning("数据目录不存在，将尝试创建: {Directory}", directory);
            Directory.CreateDirectory(directory);
        }
    }
    
    private bool TryGetFromCache(string cacheKey, out List<LotteryDataRow> data)
    {
        data = new List<LotteryDataRow>();
        
        if (!_dataCache.TryGetValue(cacheKey, out var cachedData) || 
            !_cacheTimestamps.TryGetValue(cacheKey, out var timestamp))
        {
            return false;
        }
        
        if (DateTime.Now - timestamp > _fileCacheExpiry)
        {
            _dataCache.TryRemove(cacheKey, out _);
            _cacheTimestamps.TryRemove(cacheKey, out _);
            return false;
        }
        
        data = cachedData;
        return true;
    }
    
    private void UpdateCache(string cacheKey, List<LotteryDataRow> data)
    {
        _dataCache.AddOrUpdate(cacheKey, data, (key, oldValue) => data);
        _cacheTimestamps.AddOrUpdate(cacheKey, DateTime.Now, (key, oldValue) => DateTime.Now);
    }
    
    private Task<List<LotteryDataRow>> ProcessAndValidateDataAsync(List<LotteryDataRow> data, CancellationToken cancellationToken)
    {
        var validData = new List<LotteryDataRow>();
        var invalidCount = 0;
        
        foreach (var row in data)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            var errors = ValidateRow(row);
            if (errors.Count == 0)
            {
                validData.Add(row);
            }
            else
            {
                invalidCount++;
                _logger.LogDebug("无效数据行 - 期号: {PeriodNumber}, 错误: {Errors}", 
                    row.PeriodNumber, string.Join(", ", errors));
            }
        }
        
        if (invalidCount > 0)
        {
            _logger.LogWarning("数据验证完成 - 总记录: {Total}, 有效记录: {Valid}, 无效记录: {Invalid}", 
                data.Count, validData.Count, invalidCount);
        }
        
        return Task.FromResult(validData.OrderBy(x => x.DrawDate).ToList());
    }
    
    private List<string> ValidateRow(LotteryDataRow row)
    {
        var errors = new List<string>();
        
        if (string.IsNullOrWhiteSpace(row.PeriodNumber))
        {
            errors.Add("期号为空");
        }
        
        if (row.RedBalls == null || row.RedBalls.Length != 5)
        {
            errors.Add("红球数量不正确");
        }
        else
        {
            foreach (var ball in row.RedBalls)
            {
                if (ball < 1 || ball > 35)
                {
                    errors.Add($"红球号码超出范围: {ball}");
                }
            }
            
            if (row.RedBalls.Distinct().Count() != row.RedBalls.Length)
            {
                errors.Add("红球号码重复");
            }
        }
        
        if (row.BlueBalls == null || row.BlueBalls.Length != 2)
        {
            errors.Add("蓝球数量不正确");
        }
        else
        {
            foreach (var ball in row.BlueBalls)
            {
                if (ball < 1 || ball > 12)
                {
                    errors.Add($"蓝球号码超出范围: {ball}");
                }
            }
            
            if (row.BlueBalls.Distinct().Count() != row.BlueBalls.Length)
            {
                errors.Add("蓝球号码重复");
            }
        }
        
        if (row.DrawDate == default)
        {
            errors.Add("开奖日期无效");
        }
        
        return errors;
    }
}