using DLT_CP.Models;
using DLT_CP.Services;
using DLT_CP.Commands;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace DLT_CP.ViewModels;

/// <summary>
/// 主窗口视图模型
/// </summary>
public class MainViewModel : INotifyPropertyChanged
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IDataService _dataService;
    private readonly IBacktestFramework _backtestFramework;
    private readonly FrequencyAnalyzer _frequencyAnalyzer;
    private readonly MarkovChainPredictor _markovChainPredictor;
    private readonly MLNetPredictor _mlNetPredictor;
    private readonly TorchSharpPredictor _torchSharpPredictor;
    private readonly ILogger<MainViewModel> _logger;
    private readonly GlobalExceptionHandler _exceptionHandler;
    private readonly PerformanceMonitor _performanceMonitor;
    private readonly MemoryManager _memoryManager;
    private readonly CacheService _cacheService;
    
    private string _selectedPredictor = "FrequencyAnalyzer";
    private string _nextPeriodNumber = "";
    private bool _isTraining;
    private bool _isPredicting;
    private bool _isBacktesting;
    private string _statusMessage = "就绪";
    private double _progressValue;
    private PredictionResult? _currentPrediction;
    private BacktestResult? _currentBacktestResult;
    
    public MainViewModel(
        IServiceProvider serviceProvider,
        IDataService dataService,
        IBacktestFramework backtestFramework,
        FrequencyAnalyzer frequencyAnalyzer,
        MarkovChainPredictor markovChainPredictor,
        MLNetPredictor mlNetPredictor,
        TorchSharpPredictor torchSharpPredictor,
        ILogger<MainViewModel> logger,
        GlobalExceptionHandler exceptionHandler,
        PerformanceMonitor performanceMonitor,
        MemoryManager memoryManager,
        CacheService cacheService)
    {
        _serviceProvider = serviceProvider;
        _dataService = dataService;
        _backtestFramework = backtestFramework;
        _frequencyAnalyzer = frequencyAnalyzer;
        _markovChainPredictor = markovChainPredictor;
        _mlNetPredictor = mlNetPredictor;
        _torchSharpPredictor = torchSharpPredictor;
        _logger = logger;
        _exceptionHandler = exceptionHandler;
        _performanceMonitor = performanceMonitor;
        _memoryManager = memoryManager;
        _cacheService = cacheService;
        
        // 初始化集合
        HistoricalData = new ObservableCollection<LotteryDataRow>();
        PredictionHistory = new ObservableCollection<PredictionResult>();
        BacktestResults = new ObservableCollection<BacktestResult>();
        
        // 初始化命令
        LoadDataCommand = new RelayCommand(async () => await LoadDataAsync(), () => !IsTraining && !IsPredicting);
        TrainModelCommand = new RelayCommand(async () => await TrainModelAsync(), () => !IsTraining && !IsPredicting && HistoricalData.Any());
        PredictCommand = new RelayCommand(async () => await PredictAsync(), () => !IsTraining && !IsPredicting && !string.IsNullOrWhiteSpace(NextPeriodNumber));
        RunBacktestCommand = new RelayCommand(async () => await RunBacktestAsync(), () => !IsTraining && !IsPredicting && !IsBacktesting && HistoricalData.Any());
        ExportDataCommand = new RelayCommand(async () => await ExportDataAsync(), () => HistoricalData.Any());
        ClearHistoryCommand = new RelayCommand(() => ClearHistory(), () => PredictionHistory.Any());
        ClearDataCommand = new RelayCommand(async () => await ClearDataAsync(), () => HistoricalData.Any());
        
        // 初始化数据
        _ = InitializeAsync();
    }
    
    #region 属性
    
    /// <summary>
    /// 可用的预测器列表
    /// </summary>
    public List<string> AvailablePredictors { get; } = new()
    {
        "FrequencyAnalyzer",
        "MarkovChainPredictor",
        "MLNetPredictor",
        "TorchSharpPredictor"
    };
    
    /// <summary>
    /// 选中的预测器
    /// </summary>
    public string SelectedPredictor
    {
        get => _selectedPredictor;
        set
        {
            if (SetProperty(ref _selectedPredictor, value))
            {
                OnPropertyChanged(nameof(PredictorDescription));
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }
    
    /// <summary>
    /// 预测器描述
    /// </summary>
    public string PredictorDescription
    {
        get
        {
            return SelectedPredictor switch
            {
                "FrequencyAnalyzer" => "基于历史号码出现频率进行预测，适合寻找热门号码",
                "MarkovChainPredictor" => "基于马尔可夫链状态转移概率预测，考虑号码间的关联性",
                "MLNetPredictor" => "使用ML.NET机器学习框架，支持多种算法的集成预测",
                "TorchSharpPredictor" => "使用TorchSharp深度学习框架，神经网络模型预测",
                _ => "未知预测器"
            };
        }
    }
    
    /// <summary>
    /// 下一期期号
    /// </summary>
    public string NextPeriodNumber
    {
        get => _nextPeriodNumber;
        set
        {
            if (SetProperty(ref _nextPeriodNumber, value))
            {
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }
    
    /// <summary>
    /// 是否正在训练
    /// </summary>
    public bool IsTraining
    {
        get => _isTraining;
        set
        {
            if (SetProperty(ref _isTraining, value))
            {
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }
    
    /// <summary>
    /// 是否正在预测
    /// </summary>
    public bool IsPredicting
    {
        get => _isPredicting;
        set
        {
            if (SetProperty(ref _isPredicting, value))
            {
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }
    
    /// <summary>
    /// 是否正在回测
    /// </summary>
    public bool IsBacktesting
    {
        get => _isBacktesting;
        set
        {
            if (SetProperty(ref _isBacktesting, value))
            {
                CommandManager.InvalidateRequerySuggested();
            }
        }
    }
    
    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }
    
    /// <summary>
    /// 进度值
    /// </summary>
    public double ProgressValue
    {
        get => _progressValue;
        set => SetProperty(ref _progressValue, value);
    }
    
    /// <summary>
    /// 当前预测结果
    /// </summary>
    public PredictionResult? CurrentPrediction
    {
        get => _currentPrediction;
        set => SetProperty(ref _currentPrediction, value);
    }
    
    /// <summary>
    /// 当前回测结果
    /// </summary>
    public BacktestResult? CurrentBacktestResult
    {
        get => _currentBacktestResult;
        set => SetProperty(ref _currentBacktestResult, value);
    }
    
    /// <summary>
    /// 历史数据
    /// </summary>
    public ObservableCollection<LotteryDataRow> HistoricalData { get; }
    
    /// <summary>
    /// 预测历史
    /// </summary>
    public ObservableCollection<PredictionResult> PredictionHistory { get; }
    
    /// <summary>
    /// 回测结果
    /// </summary>
    public ObservableCollection<BacktestResult> BacktestResults { get; }
    
    #endregion
    
    #region 命令
    
    public ICommand LoadDataCommand { get; }
    public ICommand TrainModelCommand { get; }
    public ICommand PredictCommand { get; }
    public ICommand RunBacktestCommand { get; }
    public ICommand ExportDataCommand { get; }
    public ICommand ClearHistoryCommand { get; }
    public ICommand ClearDataCommand { get; }
    
    #endregion
    
    #region 方法
    
    private async Task InitializeAsync()
    {
        try
        {
            StatusMessage = "初始化中...";
            
            // 自动生成下一期期号
            await GenerateNextPeriodNumberAsync();
            
            // 尝试加载历史数据
            await LoadDataAsync();
            
            StatusMessage = "就绪";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化失败");
            StatusMessage = $"初始化失败: {ex.Message}";
        }
    }
    
    private async Task LoadDataAsync()
    {
        var operationId = _performanceMonitor.StartOperation("LoadData", "用户触发数据加载");
        
        try
        {
            StatusMessage = "加载历史数据中...";
            ProgressValue = 0;
            
            // 检查缓存
            var cachedData = await _cacheService.GetAsync<List<LotteryDataRow>>("lottery_data");
            List<LotteryDataRow> data;
            
            if (cachedData != null)
            {
                data = cachedData;
                StatusMessage = "从缓存加载数据...";
                _logger.LogInformation("从缓存加载数据，记录数: {Count}", data.Count);
            }
            else
            {
                data = await _dataService.LoadDataAsync();
                
                // 缓存数据
                await _cacheService.SetAsync("lottery_data", data, TimeSpan.FromHours(1));
                _logger.LogInformation("数据已缓存，记录数: {Count}", data.Count);
            }
            
            HistoricalData.Clear();
            foreach (var item in data.OrderByDescending(x => x.DrawDate))
            {
                HistoricalData.Add(item);
            }
            
            ProgressValue = 100;
            StatusMessage = $"已加载 {HistoricalData.Count} 期历史数据";
            
            _logger.LogInformation("历史数据加载完成，共 {Count} 期", HistoricalData.Count);
            
            // 触发内存优化
            _memoryManager.OptimizeMemory();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载历史数据失败");
            StatusMessage = $"加载数据失败: {ex.Message}";
            _exceptionHandler.HandleException(ex, "数据加载操作失败");
        }
        finally
        {
            ProgressValue = 0;
            _performanceMonitor.EndOperation(operationId, $"加载了 {HistoricalData.Count} 条记录");
        }
    }
    
    private async Task TrainModelAsync()
    {
        var operationId = _performanceMonitor.StartOperation("TrainModel", $"训练模型: {SelectedPredictor}");
        
        try
        {
            IsTraining = true;
            StatusMessage = "训练模型中...";
            ProgressValue = 0;
            
            var predictor = GetCurrentPredictor();
            if (predictor == null)
            {
                StatusMessage = "无法获取预测器实例";
                return;
            }
            
            // 检查内存使用情况
            var memoryBefore = GC.GetTotalMemory(false);
            _memoryManager.RegisterComponent($"训练-{predictor.Name}", memoryBefore);
            
            var trainingData = HistoricalData.OrderBy(x => x.DrawDate).ToList();
            
            // 模拟训练进度
            var progress = new Progress<double>(value => ProgressValue = value);
            
            await predictor.TrainAsync(trainingData, progress, CancellationToken.None);
            
            // 记录内存使用
            var memoryAfter = GC.GetTotalMemory(false);
            var memoryUsed = memoryAfter - memoryBefore;
            
            ProgressValue = 100;
            StatusMessage = $"模型训练完成 - {predictor.Name}";
            
            _logger.LogInformation("模型训练完成: {PredictorName}, 内存使用: {MemoryUsed}MB", 
                predictor.Name, memoryUsed / (1024.0 * 1024));
            
            // 触发内存优化
            _memoryManager.OptimizeMemory();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模型训练失败");
            StatusMessage = $"训练失败: {ex.Message}";
            _exceptionHandler.HandleException(ex, $"模型训练失败: {SelectedPredictor}");
        }
        finally
        {
            IsTraining = false;
            ProgressValue = 0;
            _performanceMonitor.EndOperation(operationId, $"训练数据量: {HistoricalData.Count}");
        }
    }
    
    private async Task PredictAsync()
    {
        var operationId = _performanceMonitor.StartOperation("Predict", $"预测: {SelectedPredictor}");
        
        try
        {
            IsPredicting = true;
            StatusMessage = "预测中...";
            ProgressValue = 50;
            
            var predictor = GetCurrentPredictor();
            if (predictor == null)
            {
                StatusMessage = "无法获取预测器实例";
                return;
            }
            
            if (!predictor.IsModelTrained)
            {
                StatusMessage = "模型尚未训练，请先训练模型";
                return;
            }
            
            // 检查预测缓存
            var cacheKey = $"prediction_{SelectedPredictor}_{NextPeriodNumber}";
            var cachedPrediction = await _cacheService.GetAsync<PredictionResult>(cacheKey);
            
            PredictionResult result;
            if (cachedPrediction != null)
            {
                result = cachedPrediction;
                StatusMessage = "从缓存获取预测结果...";
                _logger.LogInformation("使用缓存的预测结果: {PredictorName}, 期号: {PeriodNumber}", 
                    SelectedPredictor, NextPeriodNumber);
            }
            else
            {
                result = await predictor.PredictAsync(NextPeriodNumber, CancellationToken.None);
                
                // 缓存预测结果
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(30));
                _logger.LogInformation("预测结果已缓存: {PredictorName}, 期号: {PeriodNumber}", 
                    SelectedPredictor, NextPeriodNumber);
            }
            
            CurrentPrediction = result;
            PredictionHistory.Insert(0, result);
            
            ProgressValue = 100;
            StatusMessage = $"预测完成 - 期号: {result.PeriodNumber}, 置信度: {result.Confidence:F1}%";
            
            _logger.LogInformation("预测完成: {PeriodNumber}, 置信度: {Confidence:F2}%", 
                result.PeriodNumber, result.Confidence);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测失败");
            StatusMessage = $"预测失败: {ex.Message}";
            _exceptionHandler.HandleException(ex, $"预测失败: {SelectedPredictor}");
        }
        finally
        {
            IsPredicting = false;
            ProgressValue = 0;
            _performanceMonitor.EndOperation(operationId, CurrentPrediction != null ? $"置信度: {CurrentPrediction.Confidence:F1}%" : "预测失败");
        }
    }
    
    private async Task RunBacktestAsync()
    {
        var operationId = _performanceMonitor.StartOperation("Backtest", $"回测: {SelectedPredictor}");
        
        try
        {
            IsBacktesting = true;
            StatusMessage = "执行回测中...";
            ProgressValue = 0;
            
            var predictor = GetCurrentPredictor();
            if (predictor == null)
            {
                StatusMessage = "无法获取预测器实例";
                return;
            }
            
            var testData = HistoricalData.OrderBy(x => x.DrawDate).ToList();
            if (testData.Count < 100)
            {
                StatusMessage = "回测数据不足，至少需要100期数据";
                return;
            }
            
            // 使用最近50期进行回测
            var backtestData = testData.TakeLast(50).ToList();
            
            // 检查回测缓存
            var cacheKey = $"backtest_{SelectedPredictor}_{backtestData.Count}_30";
            var cachedResult = await _cacheService.GetAsync<BacktestResult>(cacheKey);
            
            BacktestResult result;
            if (cachedResult != null)
            {
                result = cachedResult;
                StatusMessage = "从缓存获取回测结果...";
                _logger.LogInformation("使用缓存的回测结果: {PredictorName}", predictor.Name);
            }
            else
            {
                // 检查内存使用情况
                var memoryBefore = GC.GetTotalMemory(false);
                _memoryManager.RegisterComponent($"回测-{predictor.Name}", memoryBefore);
                
                var progress = new Progress<BacktestProgress>(p => 
                {
                    ProgressValue = p.CompletedCount * 100.0 / p.TotalCount;
                    StatusMessage = $"回测进度: {p.CompletedCount}/{p.TotalCount}";
                });
                
                result = await _backtestFramework.RunBacktestAsync(
                    predictor, backtestData, 30, progress, CancellationToken.None);
                
                // 记录内存使用
                var memoryAfter = GC.GetTotalMemory(false);
                var memoryUsed = memoryAfter - memoryBefore;
                
                // 缓存回测结果
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromHours(2));
                
                _logger.LogInformation("回测结果已缓存: {PredictorName}, 内存使用: {MemoryUsed}MB", 
                    predictor.Name, memoryUsed / (1024.0 * 1024));
            }
            
            CurrentBacktestResult = result;
            BacktestResults.Insert(0, result);
            
            ProgressValue = 100;
            StatusMessage = $"回测完成 - 综合评分: {result.OverallScore:F1}, 平均命中: {result.AverageRedHits:F1}红球 {result.AverageBlueHits:F1}蓝球";
            
            _logger.LogInformation("回测完成: {ModelName}, 评分: {Score:F2}", 
                result.ModelName, result.OverallScore);
            
            // 触发内存优化
            _memoryManager.OptimizeMemory();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "回测失败");
            StatusMessage = $"回测失败: {ex.Message}";
            _exceptionHandler.HandleException(ex, $"回测失败: {SelectedPredictor}");
        }
        finally
        {
            IsBacktesting = false;
            ProgressValue = 0;
            _performanceMonitor.EndOperation(operationId, CurrentBacktestResult != null ? 
                $"综合评分: {CurrentBacktestResult.OverallScore:F1}" : "回测失败");
        }
    }
    
    private async Task ExportDataAsync()
    {
        try
        {
            StatusMessage = "导出数据中...";
            
            var fileName = $"大乐透数据_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
            
            await _dataService.ExportDataAsync(HistoricalData.ToList(), filePath, ExportFormat.CSV);
            
            StatusMessage = $"数据已导出到: {filePath}";
            _logger.LogInformation("数据导出完成: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出数据失败");
            StatusMessage = $"导出失败: {ex.Message}";
        }
    }
    
    private void ClearHistory()
    {
        PredictionHistory.Clear();
        CurrentPrediction = null;
        StatusMessage = "预测历史已清空";
    }
    
    private async Task ClearDataAsync()
    {
        try
        {
            HistoricalData.Clear();
            CurrentPrediction = null;
            CurrentBacktestResult = null;
            
            // 清理相关缓存
            await _cacheService.ClearAsync();
            
            // 强制垃圾回收
            _memoryManager.ForceGarbageCollection();
            
            StatusMessage = "数据和缓存已清空";
            _logger.LogInformation("数据和缓存已清空");
        }
        catch (Exception ex)
        {
            StatusMessage = $"清空数据失败: {ex.Message}";
            _exceptionHandler.HandleException(ex, "清空数据失败");
        }
    }
    
    private async Task GenerateNextPeriodNumberAsync()
    {
        try
        {
            var latestData = await _dataService.GetLatestDataAsync(1);
            if (latestData.Any())
            {
                var latest = latestData.First();
                if (int.TryParse(latest.PeriodNumber, out var periodNum))
                {
                    NextPeriodNumber = (periodNum + 1).ToString("D3");
                }
                else
                {
                    NextPeriodNumber = DateTime.Now.ToString("yyMMdd");
                }
            }
            else
            {
                NextPeriodNumber = DateTime.Now.ToString("yyMMdd");
            }
        }
        catch
        {
            NextPeriodNumber = DateTime.Now.ToString("yyMMdd");
        }
    }
    
    private IPredictor? GetCurrentPredictor()
    {
        return SelectedPredictor switch
        {
            "FrequencyAnalyzer" => _serviceProvider.GetService<FrequencyAnalyzer>(),
            "MarkovChainPredictor" => _serviceProvider.GetService<MarkovChainPredictor>(),
            "MLNetPredictor" => _serviceProvider.GetService<MLNetPredictor>(),
            "TorchSharpPredictor" => _serviceProvider.GetService<TorchSharpPredictor>(),
            _ => null
        };
    }
    
    #endregion
    
    #region INotifyPropertyChanged
    
    public event PropertyChangedEventHandler? PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;
        
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
    
    #endregion
}