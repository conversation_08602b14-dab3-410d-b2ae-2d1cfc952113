using System;

namespace DLT_CP.Exceptions;

/// <summary>
/// 预测相关异常
/// </summary>
public class PredictionException : Exception
{
    public string PredictorName { get; }
    public string? PeriodNumber { get; }
    
    public PredictionException(string message) : base(message)
    {
        PredictorName = string.Empty;
    }
    
    public PredictionException(string message, Exception innerException) : base(message, innerException)
    {
        PredictorName = string.Empty;
    }
    
    public PredictionException(string message, string predictorName, string? periodNumber = null) : base(message)
    {
        PredictorName = predictorName;
        PeriodNumber = periodNumber;
    }
    
    public PredictionException(string message, string predictorName, Exception innerException, string? periodNumber = null) 
        : base(message, innerException)
    {
        PredictorName = predictorName;
        PeriodNumber = periodNumber;
    }
}

/// <summary>
/// 数据处理相关异常
/// </summary>
public class DataProcessingException : Exception
{
    public string? FilePath { get; }
    public int? LineNumber { get; }
    
    public DataProcessingException(string message) : base(message)
    {
    }
    
    public DataProcessingException(string message, Exception innerException) : base(message, innerException)
    {
    }
    
    public DataProcessingException(string message, string? filePath, int? lineNumber = null) : base(message)
    {
        FilePath = filePath;
        LineNumber = lineNumber;
    }
    
    public DataProcessingException(string message, string? filePath, Exception innerException, int? lineNumber = null) 
        : base(message, innerException)
    {
        FilePath = filePath;
        LineNumber = lineNumber;
    }
}

/// <summary>
/// 回测相关异常
/// </summary>
public class BacktestException : Exception
{
    public string? ModelName { get; }
    public int? PeriodIndex { get; }
    
    public BacktestException(string message) : base(message)
    {
    }
    
    public BacktestException(string message, Exception innerException) : base(message, innerException)
    {
    }
    
    public BacktestException(string message, string? modelName, int? periodIndex = null) : base(message)
    {
        ModelName = modelName;
        PeriodIndex = periodIndex;
    }
    
    public BacktestException(string message, string? modelName, Exception innerException, int? periodIndex = null) 
        : base(message, innerException)
    {
        ModelName = modelName;
        PeriodIndex = periodIndex;
    }
}

/// <summary>
/// 模型训练相关异常
/// </summary>
public class ModelTrainingException : Exception
{
    public string? ModelType { get; }
    public int? EpochNumber { get; }
    
    public ModelTrainingException(string message) : base(message)
    {
    }
    
    public ModelTrainingException(string message, Exception innerException) : base(message, innerException)
    {
    }
    
    public ModelTrainingException(string message, string? modelType, int? epochNumber = null) : base(message)
    {
        ModelType = modelType;
        EpochNumber = epochNumber;
    }
    
    public ModelTrainingException(string message, string? modelType, Exception innerException, int? epochNumber = null) 
        : base(message, innerException)
    {
        ModelType = modelType;
        EpochNumber = epochNumber;
    }
}