using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using DLT_CP.Commands;
using DLT_CP.Services;
using Microsoft.Extensions.Logging;

namespace DLT_CP.ViewModels
{
    public class PerformanceMonitorViewModel : INotifyPropertyChanged
    {
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly CacheService _cacheService;
        private readonly MemoryManager _memoryManager;
        private readonly ILogger<PerformanceMonitorViewModel> _logger;
        private readonly DispatcherTimer _refreshTimer;
        private readonly System.Diagnostics.PerformanceCounter _cpuCounter;
        
        private double _cpuUsage;
        private double _memoryUsageMB;
        private double _memoryUsagePercent;
        private int _activeOperations;
        private double _cacheHitRate;
        private double _averageResponseTime;
        private double _errorRate;
        private DateTime _lastUpdateTime;
        private bool _autoRefresh = true;
        private string _selectedTimeRange = "最近1小时";
        private string _refreshInterval = "10秒";
        
        public PerformanceMonitorViewModel(
            PerformanceMonitor performanceMonitor,
            CacheService cacheService,
            MemoryManager memoryManager,
            ILogger<PerformanceMonitorViewModel> logger)
        {
            _performanceMonitor = performanceMonitor;
            _cacheService = cacheService;
            _memoryManager = memoryManager;
            _logger = logger;
            
            // 初始化性能计数器
            _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            
            // 初始化集合
            RecentLogs = new ObservableCollection<LogEntry>();
            PerformanceHistory = new ObservableCollection<PerformanceEntry>();
            CacheDetails = new ObservableCollection<CacheEntry>();
            
            // 初始化命令
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
            ClearHistoryCommand = new RelayCommand(ClearHistory);
            ExportReportCommand = new RelayCommand(async () => await ExportReportAsync());
            ApplyTimeRangeCommand = new RelayCommand(async () => await ApplyTimeRangeAsync());
            
            // 初始化定时器
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(10)
            };
            _refreshTimer.Tick += async (s, e) => await RefreshDataAsync();
            
            // 开始监控
            StartMonitoring();
        }
        
        #region 属性
        
        public double CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
            }
        }
        
        public double MemoryUsageMB
        {
            get => _memoryUsageMB;
            set
            {
                _memoryUsageMB = value;
                OnPropertyChanged();
            }
        }
        
        public double MemoryUsagePercent
        {
            get => _memoryUsagePercent;
            set
            {
                _memoryUsagePercent = value;
                OnPropertyChanged();
            }
        }
        
        public int ActiveOperations
        {
            get => _activeOperations;
            set
            {
                _activeOperations = value;
                OnPropertyChanged();
            }
        }
        
        public double CacheHitRate
        {
            get => _cacheHitRate;
            set
            {
                _cacheHitRate = value;
                OnPropertyChanged();
            }
        }
        
        public double AverageResponseTime
        {
            get => _averageResponseTime;
            set
            {
                _averageResponseTime = value;
                OnPropertyChanged();
            }
        }
        
        public double ErrorRate
        {
            get => _errorRate;
            set
            {
                _errorRate = value;
                OnPropertyChanged();
            }
        }
        
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                _lastUpdateTime = value;
                OnPropertyChanged();
            }
        }
        
        public bool AutoRefresh
        {
            get => _autoRefresh;
            set
            {
                _autoRefresh = value;
                OnPropertyChanged();
                
                if (value)
                    _refreshTimer.Start();
                else
                    _refreshTimer.Stop();
            }
        }
        
        public string SelectedTimeRange
        {
            get => _selectedTimeRange;
            set
            {
                _selectedTimeRange = value;
                OnPropertyChanged();
            }
        }
        
        public string RefreshInterval
        {
            get => _refreshInterval;
            set
            {
                _refreshInterval = value;
                OnPropertyChanged();
                UpdateRefreshInterval();
            }
        }
        
        // 缓存统计属性
        public int TotalCacheItems { get; private set; }
        public long CacheHits { get; private set; }
        public long CacheMisses { get; private set; }
        public double CacheSizeMB { get; private set; }
        
        #endregion
        
        #region 集合
        
        public ObservableCollection<LogEntry> RecentLogs { get; }
        public ObservableCollection<PerformanceEntry> PerformanceHistory { get; }
        public ObservableCollection<CacheEntry> CacheDetails { get; }
        
        #endregion
        
        #region 命令
        
        public ICommand RefreshCommand { get; }
        public ICommand ClearHistoryCommand { get; }
        public ICommand ExportReportCommand { get; }
        public ICommand ApplyTimeRangeCommand { get; }
        
        #endregion
        
        #region 方法
        
        private void StartMonitoring()
        {
            Task.Run(async () =>
            {
                await RefreshDataAsync();
                if (AutoRefresh)
                    _refreshTimer.Start();
            });
        }
        
        private async Task RefreshDataAsync()
        {
            try
            {
                // 更新CPU使用率
                CpuUsage = _cpuCounter.NextValue();
                
                // 更新内存使用
                var memoryReport = _memoryManager.GetMemoryReport();
                MemoryUsageMB = memoryReport.TotalMemoryMB;
                MemoryUsagePercent = memoryReport.MemoryPressureLevel * 25; // 转换为百分比
                
                // 更新性能指标
                var performanceReport = _performanceMonitor.GetPerformanceReport();
                ActiveOperations = performanceReport.ActiveOperations;
                AverageResponseTime = performanceReport.AverageResponseTime;
                ErrorRate = performanceReport.ErrorRate;
                
                // 更新缓存统计
                await UpdateCacheStatisticsAsync();
                
                // 更新性能历史
                await UpdatePerformanceHistoryAsync();
                
                // 更新日志
                UpdateRecentLogs();
                
                LastUpdateTime = DateTime.Now;
                
                _logger.LogDebug("性能监控数据已更新");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新性能监控数据失败");
            }
        }
        
        private async Task UpdateCacheStatisticsAsync()
        {
            try
            {
                var cacheReport = await _cacheService.GetCacheReportAsync();
                
                TotalCacheItems = cacheReport.TotalItems;
                CacheHits = cacheReport.HitCount;
                CacheMisses = cacheReport.MissCount;
                CacheSizeMB = cacheReport.TotalSizeMB;
                
                CacheHitRate = CacheHits + CacheMisses > 0 ? 
                    (double)CacheHits / (CacheHits + CacheMisses) * 100 : 0;
                
                // 更新缓存详情
                CacheDetails.Clear();
                foreach (var item in cacheReport.Items.Take(100)) // 只显示前100项
                {
                    CacheDetails.Add(new CacheEntry
                    {
                        Key = item.Key,
                        SizeKB = item.SizeBytes / 1024.0,
                        HitCount = item.HitCount,
                        CreatedAt = item.CreatedAt,
                        LastAccessed = item.LastAccessed,
                        ExpiresAt = item.ExpiresAt
                    });
                }
                
                OnPropertyChanged(nameof(TotalCacheItems));
                OnPropertyChanged(nameof(CacheHits));
                OnPropertyChanged(nameof(CacheMisses));
                OnPropertyChanged(nameof(CacheSizeMB));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新缓存统计失败");
            }
        }
        
        private async Task UpdatePerformanceHistoryAsync()
        {
            try
            {
                var timeRange = GetTimeRangeFromSelection();
                var history = _performanceMonitor.GetPerformanceHistory(timeRange);
                
                PerformanceHistory.Clear();
                foreach (var entry in history.OrderByDescending(x => x.Timestamp).Take(1000))
                {
                    PerformanceHistory.Add(new PerformanceEntry
                    {
                        Timestamp = entry.Timestamp,
                        OperationName = entry.OperationName,
                        Duration = entry.Duration.TotalMilliseconds,
                        CpuUsage = entry.CpuUsage,
                        MemoryUsage = entry.MemoryUsage / (1024.0 * 1024), // 转换为MB
                        Status = entry.IsSuccess ? "成功" : "失败",
                        Details = entry.Details
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新性能历史失败");
            }
        }
        
        private void UpdateRecentLogs()
        {
            // 这里可以从日志系统获取最近的日志条目
            // 为了演示，我们添加一些模拟数据
            if (RecentLogs.Count > 50)
            {
                while (RecentLogs.Count > 50)
                {
                    RecentLogs.RemoveAt(0);
                }
            }
            
            // 添加当前状态日志
            RecentLogs.Add(new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = "INFO",
                Message = $"性能监控更新 - CPU: {CpuUsage:F1}%, 内存: {MemoryUsageMB:F0}MB, 活跃操作: {ActiveOperations}"
            });
        }
        
        private TimeSpan GetTimeRangeFromSelection()
        {
            return SelectedTimeRange switch
            {
                "最近1小时" => TimeSpan.FromHours(1),
                "最近6小时" => TimeSpan.FromHours(6),
                "最近24小时" => TimeSpan.FromDays(1),
                "最近7天" => TimeSpan.FromDays(7),
                _ => TimeSpan.FromHours(1)
            };
        }
        
        private void UpdateRefreshInterval()
        {
            var interval = RefreshInterval switch
            {
                "5秒" => TimeSpan.FromSeconds(5),
                "10秒" => TimeSpan.FromSeconds(10),
                "30秒" => TimeSpan.FromSeconds(30),
                "60秒" => TimeSpan.FromSeconds(60),
                _ => TimeSpan.FromSeconds(10)
            };
            
            _refreshTimer.Interval = interval;
        }
        
        private void ClearHistory()
        {
            PerformanceHistory.Clear();
            RecentLogs.Clear();
            _performanceMonitor.ClearHistory();
            _logger.LogInformation("性能监控历史已清空");
        }
        
        private async Task ExportReportAsync()
        {
            try
            {
                var report = await GenerateReportAsync();
                var fileName = $"PerformanceReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
                
                await File.WriteAllTextAsync(filePath, report);
                
                _logger.LogInformation("性能报告已导出: {FilePath}", filePath);
                
                // 可以添加消息框通知用户
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出性能报告失败");
            }
        }
        
        private async Task<string> GenerateReportAsync()
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== 彩票预测系统性能报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            // 系统概览
            report.AppendLine("=== 系统概览 ===");
            report.AppendLine($"CPU使用率: {CpuUsage:F1}%");
            report.AppendLine($"内存使用: {MemoryUsageMB:F0} MB ({MemoryUsagePercent:F1}%)");
            report.AppendLine($"活跃操作: {ActiveOperations}");
            report.AppendLine($"平均响应时间: {AverageResponseTime:F0} ms");
            report.AppendLine($"错误率: {ErrorRate:F2}%");
            report.AppendLine();
            
            // 缓存统计
            report.AppendLine("=== 缓存统计 ===");
            report.AppendLine($"总缓存项: {TotalCacheItems}");
            report.AppendLine($"命中次数: {CacheHits}");
            report.AppendLine($"未命中次数: {CacheMisses}");
            report.AppendLine($"命中率: {CacheHitRate:F1}%");
            report.AppendLine($"缓存大小: {CacheSizeMB:F1} MB");
            report.AppendLine();
            
            // 性能历史（最近10条）
            report.AppendLine("=== 最近性能记录 ===");
            foreach (var entry in PerformanceHistory.Take(10))
            {
                report.AppendLine($"{entry.Timestamp:HH:mm:ss} | {entry.OperationName} | {entry.Duration:F0}ms | {entry.Status}");
            }
            
            return report.ToString();
        }
        
        private async Task ApplyTimeRangeAsync()
        {
            await UpdatePerformanceHistoryAsync();
        }
        
        #endregion
        
        #region INotifyPropertyChanged
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        #endregion
        
        #region 数据模型
        
        public class LogEntry
        {
            public DateTime Timestamp { get; set; }
            public string Level { get; set; }
            public string Message { get; set; }
        }
        
        public class PerformanceEntry
        {
            public DateTime Timestamp { get; set; }
            public string OperationName { get; set; }
            public double Duration { get; set; }
            public double CpuUsage { get; set; }
            public double MemoryUsage { get; set; }
            public string Status { get; set; }
            public string Details { get; set; }
        }
        
        public class CacheEntry
        {
            public string Key { get; set; }
            public double SizeKB { get; set; }
            public long HitCount { get; set; }
            public DateTime CreatedAt { get; set; }
            public DateTime LastAccessed { get; set; }
            public DateTime? ExpiresAt { get; set; }
        }
        
        #endregion
        
        #region 资源清理
        
        public void Dispose()
        {
            _refreshTimer?.Stop();
            _cpuCounter?.Dispose();
        }
        
        #endregion
    }
}