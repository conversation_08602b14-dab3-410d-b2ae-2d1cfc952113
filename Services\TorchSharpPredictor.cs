using DLT_CP.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using TorchSharp;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace DLT_CP.Services;

/// <summary>
/// TorchSharp 深度学习预测器实现
/// </summary>
public class TorchSharpPredictor : BasePredictorService
{
    private Module<Tensor, Tensor>? _redBallModel;
    private Module<Tensor, Tensor>? _blueBallModel;
    private readonly Device _device;
    private Tensor? _redBallMean;
    private Tensor? _redBallStd;
    private Tensor? _blueBallMean;
    private Tensor? _blueBallStd;
    private int _trainingDataCount;
    
    // 网络架构参数
    private readonly int _inputSize = 20; // 输入特征维度
    private readonly int _hiddenSize1 = 128;
    private readonly int _hiddenSize2 = 64;
    private readonly int _redOutputSize = 35; // 红球1-35
    private readonly int _blueOutputSize = 12; // 蓝球1-12
    
    public override string Name => "TorchSharp深度学习预测器";
    public override string Description => "使用TorchSharp深度学习框架构建神经网络进行大乐透号码预测";
    
    public TorchSharpPredictor(ILogger<TorchSharpPredictor> logger) : base(logger)
    {
        // 检查CUDA可用性
        _device = cuda.is_available() ? CUDA : CPU;
        _logger.LogInformation("使用设备: {Device}", _device.type);
        
        // 设置默认参数
        _parameters["LearningRate"] = 0.001;
        _parameters["BatchSize"] = 32;
        _parameters["Epochs"] = 100;
        _parameters["DropoutRate"] = 0.2;
        _parameters["WeightDecay"] = 0.0001;
        _parameters["HiddenSize1"] = _hiddenSize1;
        _parameters["HiddenSize2"] = _hiddenSize2;
        _parameters["SequenceLength"] = 10;
    }
    
    public override async Task<bool> TrainAsync(IEnumerable<LotteryDataRow> data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始训练TorchSharp深度学习模型...");
            
            var dataList = data.OrderBy(x => x.DrawDate).ToList();
            
            if (dataList.Count < 100)
            {
                throw new InvalidOperationException("训练数据不足，至少需要100期历史数据");
            }
            
            _trainingDataCount = dataList.Count;
            
            // 准备训练数据
            var (redTrainX, redTrainY, blueTrainX, blueTrainY) = PrepareTrainingData(dataList);
            
            // 数据标准化
            (_redBallMean, _redBallStd) = NormalizeData(redTrainX);
            (_blueBallMean, _blueBallStd) = NormalizeData(blueTrainX);
            
            redTrainX = (redTrainX - _redBallMean) / _redBallStd;
            blueTrainX = (blueTrainX - _blueBallMean) / _blueBallStd;
            
            // 创建模型
            _redBallModel = CreateRedBallModel();
            _blueBallModel = CreateBlueBallModel();
            
            // 移动到设备
            _redBallModel = _redBallModel.to(_device);
            _blueBallModel = _blueBallModel.to(_device);
            redTrainX = redTrainX.to(_device);
            redTrainY = redTrainY.to(_device);
            blueTrainX = blueTrainX.to(_device);
            blueTrainY = blueTrainY.to(_device);
            
            // 训练红球模型
            await TrainModel(_redBallModel, redTrainX, redTrainY, "红球", cancellationToken);
            
            // 训练蓝球模型
            await TrainModel(_blueBallModel, blueTrainX, blueTrainY, "蓝球", cancellationToken);
            
            IsTrained = true;
            _logger.LogInformation("TorchSharp深度学习模型训练完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "TorchSharp模型训练失败");
            return false;
        }
    }
    
    public override async Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> historyData, string nextPeriod, CancellationToken cancellationToken = default)
    {
        if (!IsTrained || _redBallModel == null || _blueBallModel == null)
        {
            throw new InvalidOperationException("模型尚未训练");
        }
        
        try
        {
            // 准备输入特征
            var inputFeatures = PrepareInputFeatures(historyData.ToList(), nextPeriod);
            
            // 标准化输入
            var redInput = ((inputFeatures.redFeatures - _redBallMean!) / _redBallStd!).to(_device);
            var blueInput = ((inputFeatures.blueFeatures - _blueBallMean!) / _blueBallStd!).to(_device);
            
            // 预测
            _redBallModel.eval();
            _blueBallModel.eval();
            
            using (no_grad())
            {
                var redOutput = _redBallModel.forward(redInput);
                var blueOutput = _blueBallModel.forward(blueInput);
                
                // 处理预测结果
                var predictedRedBalls = ProcessRedBallPrediction(redOutput);
                var predictedBlueBalls = ProcessBlueBallPrediction(blueOutput);
                
                // 计算置信度
                var confidence = CalculatePredictionConfidence(redOutput, blueOutput);
                
                var result = new PredictionResult
                {
                    IssueNumber = nextPeriod,
                    PredictionTime = DateTime.Now,
                    Red1 = predictedRedBalls[0],
                    Red2 = predictedRedBalls[1],
                    Red3 = predictedRedBalls[2],
                    Red4 = predictedRedBalls[3],
                    Red5 = predictedRedBalls[4],
                    Blue1 = predictedBlueBalls[0],
                    Blue2 = predictedBlueBalls[1],
                    Confidence = confidence,
                    ModelName = Name,
                    HistoricalPeriods = historyData.Count(),
                    ModelScore = GetModelScore(),
                    Notes = $"深度学习预测 - 隐藏层:{_hiddenSize1},{_hiddenSize2}, 学习率:{_parameters["LearningRate"]}"
                };
                
                _logger.LogDebug("TorchSharp预测完成 - 期号: {Period}, 置信度: {Confidence:F2}", nextPeriod, confidence);
                return result;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "TorchSharp预测失败");
            throw;
        }
    }
    
    public override async Task SaveModelAsync(string filePath, CancellationToken cancellationToken)
    {
        if (!IsTrained || _redBallModel == null || _blueBallModel == null)
        {
            throw new InvalidOperationException("没有训练好的模型可以保存");
        }
        
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            var redModelPath = Path.ChangeExtension(filePath, ".red.pt");
            var blueModelPath = Path.ChangeExtension(filePath, ".blue.pt");
            var normalizationPath = Path.ChangeExtension(filePath, ".norm.pt");
            var metadataPath = Path.ChangeExtension(filePath, ".metadata.json");
            
            // 保存模型
            _redBallModel.save(redModelPath);
            _blueBallModel.save(blueModelPath);
            
            // 保存标准化参数
            var normalizationData = new Dictionary<string, Tensor>
            {
                ["red_mean"] = _redBallMean!,
                ["red_std"] = _redBallStd!,
                ["blue_mean"] = _blueBallMean!,
                ["blue_std"] = _blueBallStd!
            };
            save(normalizationData, normalizationPath);
            
            // 保存元数据
            var metadata = new
            {
                ModelType = "TorchSharp",
                TrainingTime = DateTime.Now,
                Parameters = _parameters,
                TrainingDataCount = _trainingDataCount,
                ModelScore = GetModelScore(),
                Architecture = new
                {
                    InputSize = _inputSize,
                    HiddenSize1 = _hiddenSize1,
                    HiddenSize2 = _hiddenSize2,
                    RedOutputSize = _redOutputSize,
                    BlueOutputSize = _blueOutputSize
                }
            };
            
            var json = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(metadataPath, json, cancellationToken);
            
            _logger.LogInformation("TorchSharp模型已保存到: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存TorchSharp模型失败");
            throw;
        }
    }
    
    public override async Task LoadModelAsync(string filePath, CancellationToken cancellationToken)
    {
        try
        {
            var redModelPath = Path.ChangeExtension(filePath, ".red.pt");
            var blueModelPath = Path.ChangeExtension(filePath, ".blue.pt");
            var normalizationPath = Path.ChangeExtension(filePath, ".norm.pt");
            var metadataPath = Path.ChangeExtension(filePath, ".metadata.json");
            
            if (!File.Exists(redModelPath) || !File.Exists(blueModelPath))
            {
                throw new FileNotFoundException("模型文件不存在");
            }
            
            // 创建模型架构
            _redBallModel = CreateRedBallModel().to(_device);
            _blueBallModel = CreateBlueBallModel().to(_device);
            
            // 加载模型权重
            _redBallModel.load(redModelPath);
            _blueBallModel.load(blueModelPath);
            
            // 加载标准化参数
            if (File.Exists(normalizationPath))
            {
                var normalizationData = load(normalizationPath);
                _redBallMean = normalizationData["red_mean"];
                _redBallStd = normalizationData["red_std"];
                _blueBallMean = normalizationData["blue_mean"];
                _blueBallStd = normalizationData["blue_std"];
            }
            
            // 加载元数据
            if (File.Exists(metadataPath))
            {
                var json = await File.ReadAllTextAsync(metadataPath, cancellationToken);
                var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                
                if (metadata != null && metadata.ContainsKey("Parameters"))
                {
                    var parametersJson = metadata["Parameters"].ToString();
                    var loadedParameters = JsonSerializer.Deserialize<Dictionary<string, object>>(parametersJson!);
                    if (loadedParameters != null)
                    {
                        foreach (var param in loadedParameters)
                        {
                            Parameters[param.Key] = param.Value;
                        }
                    }
                }
            }
            
            IsTrained = true;
            _logger.LogInformation("TorchSharp模型已从文件加载: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载TorchSharp模型失败");
            throw;
        }
    }
    
    private Module<Tensor, Tensor> CreateRedBallModel()
    {
        return Sequential(
            ("input", Linear(_inputSize, _hiddenSize1)),
            ("relu1", ReLU()),
            ("dropout1", Dropout((double)_parameters["DropoutRate"])),
            ("hidden1", Linear(_hiddenSize1, _hiddenSize2)),
            ("relu2", ReLU()),
            ("dropout2", Dropout((double)_parameters["DropoutRate"])),
            ("output", Linear(_hiddenSize2, _redOutputSize)),
            ("sigmoid", Sigmoid())
        );
    }
    
    private Module<Tensor, Tensor> CreateBlueBallModel()
    {
        return Sequential(
            ("input", Linear(_inputSize, _hiddenSize1)),
            ("relu1", ReLU()),
            ("dropout1", Dropout((double)_parameters["DropoutRate"])),
            ("hidden1", Linear(_hiddenSize1, _hiddenSize2)),
            ("relu2", ReLU()),
            ("dropout2", Dropout((double)_parameters["DropoutRate"])),
            ("output", Linear(_hiddenSize2, _blueOutputSize)),
            ("sigmoid", Sigmoid())
        );
    }
    
    private (Tensor redTrainX, Tensor redTrainY, Tensor blueTrainX, Tensor blueTrainY) PrepareTrainingData(List<LotteryDataRow> data)
    {
        var sequenceLength = (int)_parameters["SequenceLength"];
        var samples = data.Count - sequenceLength;
        
        var redTrainX = zeros(samples, _inputSize);
        var redTrainY = zeros(samples, _redOutputSize);
        var blueTrainX = zeros(samples, _inputSize);
        var blueTrainY = zeros(samples, _blueOutputSize);
        
        for (int i = 0; i < samples; i++)
        {
            // 提取序列特征
            var sequence = data.Skip(i).Take(sequenceLength).ToList();
            var target = data[i + sequenceLength];
            
            // 红球特征
            var redFeatures = ExtractRedBallFeatures(sequence);
            redTrainX[i] = tensor(redFeatures);
            
            // 红球标签（one-hot编码）
            var redLabels = new float[_redOutputSize];
            foreach (var ball in target.RedBalls)
            {
                redLabels[ball - 1] = 1.0f;
            }
            redTrainY[i] = tensor(redLabels);
            
            // 蓝球特征
            var blueFeatures = ExtractBlueBallFeatures(sequence);
            blueTrainX[i] = tensor(blueFeatures);
            
            // 蓝球标签（one-hot编码）
            var blueLabels = new float[_blueOutputSize];
            foreach (var ball in target.BlueBalls)
            {
                blueLabels[ball - 1] = 1.0f;
            }
            blueTrainY[i] = tensor(blueLabels);
        }
        
        return (redTrainX, redTrainY, blueTrainX, blueTrainY);
    }
    
    private float[] ExtractRedBallFeatures(List<LotteryDataRow> sequence)
    {
        var features = new List<float>();
        
        // 最近一期的红球
        var lastRed = sequence.Last().RedBalls;
        features.AddRange(lastRed.Select(x => (float)x / 35.0f));
        
        // 红球统计特征
        var allRedBalls = sequence.SelectMany(x => x.RedBalls).ToList();
        features.Add((float)allRedBalls.Average() / 35.0f); // 平均值
        features.Add(allRedBalls.Count(x => x % 2 == 1) / (float)allRedBalls.Count); // 奇数比例
        features.Add(allRedBalls.Count(x => x > 17) / (float)allRedBalls.Count); // 大数比例
        features.Add(allRedBalls.Sum() / (float)(35 * 5 * sequence.Count)); // 和值比例
        
        // 时间特征
        var lastDate = sequence.Last().DrawDate;
        features.Add((float)lastDate.DayOfWeek / 7.0f);
        features.Add(lastDate.Month / 12.0f);
        features.Add(lastDate.Day / 31.0f);
        
        // 趋势特征
        if (sequence.Count >= 2)
        {
            var trend = sequence.Last().RedBalls.Sum() - sequence[sequence.Count - 2].RedBalls.Sum();
            features.Add(trend / 175.0f); // 标准化趋势
        }
        else
        {
            features.Add(0.0f);
        }
        
        // 填充到固定长度
        while (features.Count < _inputSize)
        {
            features.Add(0.0f);
        }
        
        return features.Take(_inputSize).ToArray();
    }
    
    private float[] ExtractBlueBallFeatures(List<LotteryDataRow> sequence)
    {
        var features = new List<float>();
        
        // 最近一期的蓝球
        var lastBlue = sequence.Last().BlueBalls;
        features.AddRange(lastBlue.Select(x => (float)x / 12.0f));
        
        // 蓝球统计特征
        var allBlueBalls = sequence.SelectMany(x => x.BlueBalls).ToList();
        features.Add((float)allBlueBalls.Average() / 12.0f); // 平均值
        features.Add(allBlueBalls.Count(x => x % 2 == 1) / (float)allBlueBalls.Count); // 奇数比例
        features.Add(allBlueBalls.Count(x => x > 6) / (float)allBlueBalls.Count); // 大数比例
        features.Add(allBlueBalls.Sum() / (float)(12 * 2 * sequence.Count)); // 和值比例
        
        // 时间特征
        var lastDate = sequence.Last().DrawDate;
        features.Add((float)lastDate.DayOfWeek / 7.0f);
        features.Add(lastDate.Month / 12.0f);
        features.Add(lastDate.Day / 31.0f);
        
        // 趋势特征
        if (sequence.Count >= 2)
        {
            var trend = sequence.Last().BlueBalls.Sum() - sequence[sequence.Count - 2].BlueBalls.Sum();
            features.Add(trend / 24.0f); // 标准化趋势
        }
        else
        {
            features.Add(0.0f);
        }
        
        // 填充到固定长度
        while (features.Count < _inputSize)
        {
            features.Add(0.0f);
        }
        
        return features.Take(_inputSize).ToArray();
    }
    
    private async Task TrainModel(Module<Tensor, Tensor> model, Tensor trainX, Tensor trainY, string modelType, CancellationToken cancellationToken)
    {
        var optimizer = optim.Adam(model.parameters(), lr: (double)_parameters["LearningRate"], weight_decay: (double)_parameters["WeightDecay"]);
        var criterion = nn.BCELoss();
        
        var epochs = (int)_parameters["Epochs"];
        var batchSize = (int)_parameters["BatchSize"];
        var numBatches = (int)Math.Ceiling((double)trainX.shape[0] / batchSize);
        
        model.train();
        
        for (int epoch = 0; epoch < epochs; epoch++)
        {
            if (cancellationToken.IsCancellationRequested)
                break;
            
            var totalLoss = 0.0;
            
            for (int batch = 0; batch < numBatches; batch++)
            {
                var startIdx = batch * batchSize;
                var endIdx = Math.Min(startIdx + batchSize, trainX.shape[0]);
                
                var batchX = trainX[TensorIndex.Slice(startIdx, endIdx)];
                var batchY = trainY[TensorIndex.Slice(startIdx, endIdx)];
                
                optimizer.zero_grad();
                
                var output = model.forward(batchX);
                var loss = criterion.forward(output, batchY);
                
                loss.backward();
                optimizer.step();
                
                totalLoss += loss.item<double>();
            }
            
            var avgLoss = totalLoss / numBatches;
            
            if (epoch % 10 == 0)
            {
                _logger.LogDebug("{ModelType}模型训练 - Epoch {Epoch}/{TotalEpochs}, Loss: {Loss:F4}", 
                    modelType, epoch + 1, epochs, avgLoss);
            }
        }
        
        _logger.LogInformation("{ModelType}模型训练完成", modelType);
    }
    
    private (Tensor mean, Tensor std) NormalizeData(Tensor data)
    {
        var mean = data.mean(new long[] { 0 }, keepdim: true);
        var std = data.std(new long[] { 0 }, keepdim: true);
        
        // 避免除零
        std = torch.where(std < 1e-8, torch.ones_like(std), std);
        
        return (mean, std);
    }
    
    private (Tensor redFeatures, Tensor blueFeatures) PrepareInputFeatures(List<LotteryDataRow> historyData, string nextPeriod)
    {
        if (historyData == null || !historyData.Any())
        {
            // 如果没有历史数据，使用随机特征
            var randomRedFeatures = torch.randn(1, _inputSize);
            var randomBlueFeatures = torch.randn(1, _inputSize);
            return (randomRedFeatures, randomBlueFeatures);
        }
        
        // 基于历史数据提取特征
        var sequenceLength = Math.Min((int)_parameters["SequenceLength"], historyData.Count);
        var recentData = historyData.TakeLast(sequenceLength).ToList();
        
        var redFeatures = tensor(ExtractRedBallFeatures(recentData)).unsqueeze(0);
        var blueFeatures = tensor(ExtractBlueBallFeatures(recentData)).unsqueeze(0);
        
        return (redFeatures, blueFeatures);
    }
    
    private int[] ProcessRedBallPrediction(Tensor output)
    {
        // 获取概率最高的5个红球号码
        var probabilities = output.squeeze(0).cpu().data<float>().ToArray();
        
        var ballProbabilities = probabilities
            .Select((prob, index) => new { Probability = prob, Ball = index + 1 })
            .OrderByDescending(x => x.Probability)
            .Take(5)
            .Select(x => x.Ball)
            .OrderBy(x => x)
            .ToArray();
        
        return ballProbabilities;
    }
    
    private int[] ProcessBlueBallPrediction(Tensor output)
    {
        // 获取概率最高的2个蓝球号码
        var probabilities = output.squeeze(0).cpu().data<float>().ToArray();
        
        var ballProbabilities = probabilities
            .Select((prob, index) => new { Probability = prob, Ball = index + 1 })
            .OrderByDescending(x => x.Probability)
            .Take(2)
            .Select(x => x.Ball)
            .OrderBy(x => x)
            .ToArray();
        
        return ballProbabilities;
    }
    
    private double CalculatePredictionConfidence(Tensor redOutput, Tensor blueOutput)
    {
        // 基于输出概率的熵计算置信度
        var redProbs = redOutput.squeeze(0).cpu();
        var blueProbs = blueOutput.squeeze(0).cpu();
        
        var redEntropy = CalculateEntropy(redProbs);
        var blueEntropy = CalculateEntropy(blueProbs);
        
        // 熵越小，置信度越高
        var redConfidence = Math.Max(0, 1 - redEntropy / Math.Log(35));
        var blueConfidence = Math.Max(0, 1 - blueEntropy / Math.Log(12));
        
        return (redConfidence + blueConfidence) / 2 * 100;
    }
    
    private double CalculateEntropy(Tensor probabilities)
    {
        var probs = probabilities.data<float>().ToArray();
        var entropy = 0.0;
        
        foreach (var prob in probs)
        {
            if (prob > 1e-8)
            {
                entropy -= prob * Math.Log(prob);
            }
        }
        
        return entropy;
    }
    
    private double GetModelScore()
    {
        if (!IsTrained) return 0;
        
        // 基于模型复杂度和训练数据量计算评分
        var baseScore = 70.0; // 深度学习模型基础分较高
        var dataScore = Math.Min(20, _trainingDataCount / 20.0); // 数据量加分
        var architectureScore = 10.0; // 架构复杂度加分
        
        return Math.Min(100, baseScore + dataScore + architectureScore);
    }
    
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _redBallModel?.Dispose();
            _blueBallModel?.Dispose();
            _redBallMean?.Dispose();
            _redBallStd?.Dispose();
            _blueBallMean?.Dispose();
            _blueBallStd?.Dispose();
        }
        base.Dispose(disposing);
    }
}