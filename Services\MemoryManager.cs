using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime;
using DLT_CP.Exceptions;

namespace DLT_CP.Services;

/// <summary>
/// 内存管理服务
/// </summary>
public class MemoryManager : IDisposable
{
    private readonly ILogger<MemoryManager> _logger;
    private readonly GlobalExceptionHandler? _exceptionHandler;
    private readonly Timer _memoryMonitorTimer;
    private readonly object _lockObject = new();
    
    // 内存监控配置
    private readonly long _memoryWarningThreshold;
    private readonly long _memoryCriticalThreshold;
    private readonly TimeSpan _monitoringInterval;
    
    // 内存统计
    private long _lastGCMemory;
    private DateTime _lastGCTime;
    private int _gcCount;
    private readonly Dictionary<string, MemoryUsageInfo> _componentMemoryUsage = new();
    
    public MemoryManager(ILogger<MemoryManager> logger, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _exceptionHandler = exceptionHandler;
        
        // 配置内存阈值（默认值）
        _memoryWarningThreshold = 512 * 1024 * 1024; // 512MB
        _memoryCriticalThreshold = 1024 * 1024 * 1024; // 1GB
        _monitoringInterval = TimeSpan.FromMinutes(1);
        
        // 初始化内存监控
        _lastGCMemory = GC.GetTotalMemory(false);
        _lastGCTime = DateTime.Now;
        
        // 启动内存监控定时器
        _memoryMonitorTimer = new Timer(MonitorMemoryUsage, null, _monitoringInterval, _monitoringInterval);
        
        _logger.LogInformation("内存管理器已启动 - 警告阈值: {WarningThreshold}MB, 临界阈值: {CriticalThreshold}MB", 
            _memoryWarningThreshold / (1024 * 1024), _memoryCriticalThreshold / (1024 * 1024));
    }
    
    /// <summary>
    /// 获取当前内存使用情况
    /// </summary>
    public MemoryInfo GetMemoryInfo()
    {
        try
        {
            var totalMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            var gen0Collections = GC.CollectionCount(0);
            var gen1Collections = GC.CollectionCount(1);
            var gen2Collections = GC.CollectionCount(2);
            
            return new MemoryInfo
            {
                TotalMemory = totalMemory,
                WorkingSet = workingSet,
                Gen0Collections = gen0Collections,
                Gen1Collections = gen1Collections,
                Gen2Collections = gen2Collections,
                LastGCMemory = _lastGCMemory,
                LastGCTime = _lastGCTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取内存信息时发生错误");
            return new MemoryInfo();
        }
    }
    
    /// <summary>
    /// 强制垃圾回收
    /// </summary>
    public void ForceGarbageCollection(string reason = "手动触发")
    {
        try
        {
            var beforeMemory = GC.GetTotalMemory(false);
            var stopwatch = Stopwatch.StartNew();
            
            _logger.LogDebug("开始垃圾回收 - 原因: {Reason}, 回收前内存: {BeforeMemory}MB", 
                reason, beforeMemory / (1024 * 1024));
            
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            stopwatch.Stop();
            var afterMemory = GC.GetTotalMemory(false);
            var freedMemory = beforeMemory - afterMemory;
            
            _gcCount++;
            _lastGCMemory = afterMemory;
            _lastGCTime = DateTime.Now;
            
            _logger.LogInformation("垃圾回收完成 - 原因: {Reason}, 释放内存: {FreedMemory}MB, 耗时: {Duration}ms", 
                reason, freedMemory / (1024 * 1024), stopwatch.ElapsedMilliseconds);
            
            // 记录性能警告（如果GC耗时过长）
            if (stopwatch.Elapsed > TimeSpan.FromSeconds(1))
            {
                _exceptionHandler?.LogPerformanceWarning(
                    "垃圾回收", 
                    stopwatch.Elapsed, 
                    $"原因: {reason}, 释放: {freedMemory / (1024 * 1024)}MB");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行垃圾回收时发生错误");
        }
    }
    
    /// <summary>
    /// 注册组件内存使用
    /// </summary>
    public void RegisterComponentMemoryUsage(string componentName, long memoryUsage)
    {
        lock (_lockObject)
        {
            _componentMemoryUsage[componentName] = new MemoryUsageInfo
            {
                ComponentName = componentName,
                MemoryUsage = memoryUsage,
                LastUpdated = DateTime.Now
            };
            
            _logger.LogDebug("组件内存使用已注册 - 组件: {ComponentName}, 内存: {MemoryUsage}MB", 
                componentName, memoryUsage / (1024 * 1024));
        }
    }
    
    /// <summary>
    /// 获取组件内存使用统计
    /// </summary>
    public Dictionary<string, MemoryUsageInfo> GetComponentMemoryUsage()
    {
        lock (_lockObject)
        {
            return new Dictionary<string, MemoryUsageInfo>(_componentMemoryUsage);
        }
    }
    
    /// <summary>
    /// 清理过期的组件内存记录
    /// </summary>
    public void CleanupExpiredComponentRecords(TimeSpan expiry)
    {
        lock (_lockObject)
        {
            var expiredComponents = _componentMemoryUsage
                .Where(kvp => DateTime.Now - kvp.Value.LastUpdated > expiry)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var component in expiredComponents)
            {
                _componentMemoryUsage.Remove(component);
                _logger.LogDebug("已清理过期组件内存记录 - 组件: {ComponentName}", component);
            }
        }
    }
    
    /// <summary>
    /// 检查内存压力并采取相应措施
    /// </summary>
    public MemoryPressureLevel CheckMemoryPressure()
    {
        try
        {
            var currentMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            
            if (currentMemory > _memoryCriticalThreshold || workingSet > _memoryCriticalThreshold)
            {
                _logger.LogWarning("检测到严重内存压力 - 当前内存: {CurrentMemory}MB, 工作集: {WorkingSet}MB", 
                    currentMemory / (1024 * 1024), workingSet / (1024 * 1024));
                
                // 自动触发垃圾回收
                ForceGarbageCollection("内存压力过高");
                
                return MemoryPressureLevel.Critical;
            }
            
            if (currentMemory > _memoryWarningThreshold || workingSet > _memoryWarningThreshold)
            {
                _logger.LogInformation("检测到内存压力警告 - 当前内存: {CurrentMemory}MB, 工作集: {WorkingSet}MB", 
                    currentMemory / (1024 * 1024), workingSet / (1024 * 1024));
                
                return MemoryPressureLevel.Warning;
            }
            
            return MemoryPressureLevel.Normal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查内存压力时发生错误");
            return MemoryPressureLevel.Unknown;
        }
    }
    
    /// <summary>
    /// 优化内存使用
    /// </summary>
    public void OptimizeMemoryUsage()
    {
        try
        {
            _logger.LogDebug("开始内存优化");
            
            // 清理过期的组件记录
            CleanupExpiredComponentRecords(TimeSpan.FromHours(1));
            
            // 检查内存压力
            var pressureLevel = CheckMemoryPressure();
            
            switch (pressureLevel)
            {
                case MemoryPressureLevel.Critical:
                    // 激进的内存清理
                    ForceGarbageCollection("内存优化-严重压力");
                    GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                    break;
                    
                case MemoryPressureLevel.Warning:
                    // 温和的内存清理
                    ForceGarbageCollection("内存优化-警告压力");
                    break;
                    
                case MemoryPressureLevel.Normal:
                    // 定期维护
                    if (DateTime.Now - _lastGCTime > TimeSpan.FromMinutes(10))
                    {
                        ForceGarbageCollection("内存优化-定期维护");
                    }
                    break;
            }
            
            _logger.LogDebug("内存优化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内存优化过程中发生错误");
        }
    }
    
    /// <summary>
    /// 生成内存使用报告
    /// </summary>
    public string GenerateMemoryReport()
    {
        try
        {
            var memoryInfo = GetMemoryInfo();
            var componentUsage = GetComponentMemoryUsage();
            
            var report = new List<string>
            {
                "=== 内存使用报告 ===",
                $"总内存: {memoryInfo.TotalMemory / (1024 * 1024):F2} MB",
                $"工作集: {memoryInfo.WorkingSet / (1024 * 1024):F2} MB",
                $"GC统计: Gen0={memoryInfo.Gen0Collections}, Gen1={memoryInfo.Gen1Collections}, Gen2={memoryInfo.Gen2Collections}",
                $"上次GC: {memoryInfo.LastGCTime:yyyy-MM-dd HH:mm:ss}, 内存: {memoryInfo.LastGCMemory / (1024 * 1024):F2} MB",
                $"GC次数: {_gcCount}",
                "",
                "=== 组件内存使用 ==="
            };
            
            if (componentUsage.Any())
            {
                foreach (var component in componentUsage.OrderByDescending(c => c.Value.MemoryUsage))
                {
                    report.Add($"{component.Key}: {component.Value.MemoryUsage / (1024 * 1024):F2} MB (更新时间: {component.Value.LastUpdated:HH:mm:ss})");
                }
            }
            else
            {
                report.Add("无组件内存使用记录");
            }
            
            return string.Join(Environment.NewLine, report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成内存报告时发生错误");
            return $"生成内存报告失败: {ex.Message}";
        }
    }
    
    private void MonitorMemoryUsage(object? state)
    {
        try
        {
            var pressureLevel = CheckMemoryPressure();
            
            if (pressureLevel == MemoryPressureLevel.Critical)
            {
                _logger.LogWarning("内存监控检测到严重压力，触发自动优化");
                OptimizeMemoryUsage();
            }
            
            // 定期记录内存使用情况
            var memoryInfo = GetMemoryInfo();
            _logger.LogDebug("内存监控 - 总内存: {TotalMemory}MB, 工作集: {WorkingSet}MB", 
                memoryInfo.TotalMemory / (1024 * 1024), memoryInfo.WorkingSet / (1024 * 1024));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内存监控过程中发生错误");
        }
    }
    
    public void Dispose()
    {
        try
        {
            _memoryMonitorTimer?.Dispose();
            
            _logger.LogInformation("内存管理器已释放 - 总GC次数: {GCCount}", _gcCount);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放内存管理器时发生错误");
        }
    }
}

/// <summary>
/// 内存信息
/// </summary>
public class MemoryInfo
{
    public long TotalMemory { get; set; }
    public long WorkingSet { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public long LastGCMemory { get; set; }
    public DateTime LastGCTime { get; set; }
}

/// <summary>
/// 组件内存使用信息
/// </summary>
public class MemoryUsageInfo
{
    public string ComponentName { get; set; } = string.Empty;
    public long MemoryUsage { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 内存压力级别
/// </summary>
public enum MemoryPressureLevel
{
    Normal,
    Warning,
    Critical,
    Unknown
}