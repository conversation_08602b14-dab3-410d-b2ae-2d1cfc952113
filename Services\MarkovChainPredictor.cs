using DLT_CP.Models;
using Microsoft.Extensions.Logging;

namespace DLT_CP.Services;

/// <summary>
/// 马尔可夫链预测器
/// 基于状态转移概率进行预测
/// </summary>
public class MarkovChainPredictor : BasePredictorService
{
    private Dictionary<string, Dictionary<int, double>> _redTransitionMatrix = new();
    private Dictionary<string, Dictionary<int, double>> _blueTransitionMatrix = new();
    private int _order = 2; // 马尔可夫链阶数
    private List<LotteryDataRow> _trainingData = new();
    
    public MarkovChainPredictor(ILogger<MarkovChainPredictor> logger) : base(logger)
    {
        // 设置默认参数
        _parameters["Order"] = _order;
        _parameters["SmoothingFactor"] = 0.1; // 平滑因子
        _parameters["MinTransitionCount"] = 2; // 最小转移次数
    }
    
    public override string Name => "马尔可夫链预测器";
    
    public override string Description => "基于马尔可夫链状态转移概率进行预测，分析号码序列的转移规律";
    
    public override async Task<bool> TrainAsync(IEnumerable<LotteryDataRow> data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始训练马尔可夫链模型...");
            
            if (!ValidateInputData(data))
            {
                return false;
            }
            
            _trainingData = data.OrderBy(x => x.DrawDate).ToList();
            
            // 获取参数
            if (_parameters.ContainsKey("Order"))
            {
                _order = Convert.ToInt32(_parameters["Order"]);
            }
            
            // 构建转移矩阵
            BuildTransitionMatrix(_trainingData, true); // 红球
            BuildTransitionMatrix(_trainingData, false); // 蓝球
            
            // 计算评估指标
            CalculateMetrics();
            
            IsTrained = true;
            _logger.LogInformation("马尔可夫链模型训练完成");
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练马尔可夫链模型时发生错误");
            return false;
        }
    }
    
    public override async Task<PredictionResult> PredictAsync(IEnumerable<LotteryDataRow> historyData, string nextPeriod, CancellationToken cancellationToken = default)
    {
        if (!IsTrained)
        {
            await TrainAsync(historyData, cancellationToken);
        }
        
        try
        {
            _logger.LogInformation("开始预测期号: {Period}", nextPeriod);
            
            var dataList = historyData.OrderBy(x => x.DrawDate).ToList();
            
            // 获取最近的状态序列
            var recentData = dataList.TakeLast(_order).ToList();
            
            // 预测红球
            var predictedRed = PredictBalls(recentData, true);
            
            // 预测蓝球
            var predictedBlue = PredictBalls(recentData, false);
            
            // 计算置信度
            var confidence = CalculateConfidence(predictedRed, predictedBlue, recentData);
            
            var result = new PredictionResult
            {
                PeriodNumber = nextPeriod,
                PredictionTime = DateTime.Now,
                RedBalls = predictedRed,
                BlueBalls = predictedBlue,
                Confidence = confidence,
                ModelName = Name,
                HistoryPeriods = dataList.Count,
                ModelScore = _metrics.ContainsKey("OverallScore") ? _metrics["OverallScore"] : 0,
                Notes = $"基于{_order}阶马尔可夫链的状态转移预测"
            };
            
            _logger.LogInformation("预测完成: {Numbers}", result.FullNumberString);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测时发生错误");
            throw;
        }
    }
    
    private void BuildTransitionMatrix(List<LotteryDataRow> data, bool isRedBall)
    {
        var transitionMatrix = isRedBall ? _redTransitionMatrix : _blueTransitionMatrix;
        transitionMatrix.Clear();
        
        var smoothingFactor = Convert.ToDouble(_parameters["SmoothingFactor"]);
        var maxBall = isRedBall ? 35 : 12;
        
        // 构建状态序列
        var stateSequences = new List<List<int>>();
        
        foreach (var row in data)
        {
            var balls = isRedBall ? row.RedBalls.ToList() : row.BlueBalls.ToList();
            stateSequences.Add(balls);
        }
        
        // 计算转移概率
        for (int i = _order; i < stateSequences.Count; i++)
        {
            // 构建当前状态
            var currentState = BuildStateKey(stateSequences.GetRange(i - _order, _order), isRedBall);
            var nextBalls = stateSequences[i];
            
            if (!transitionMatrix.ContainsKey(currentState))
            {
                transitionMatrix[currentState] = new Dictionary<int, double>();
                
                // 初始化所有可能的球号
                for (int ball = 1; ball <= maxBall; ball++)
                {
                    transitionMatrix[currentState][ball] = smoothingFactor;
                }
            }
            
            // 更新转移计数
            foreach (var ball in nextBalls)
            {
                transitionMatrix[currentState][ball] += 1.0;
            }
        }
        
        // 归一化为概率
        foreach (var state in transitionMatrix.Keys.ToList())
        {
            var totalCount = transitionMatrix[state].Values.Sum();
            if (totalCount > 0)
            {
                var normalizedProbs = new Dictionary<int, double>();
                foreach (var ball in transitionMatrix[state].Keys)
                {
                    normalizedProbs[ball] = transitionMatrix[state][ball] / totalCount;
                }
                transitionMatrix[state] = normalizedProbs;
            }
        }
    }
    
    private string BuildStateKey(List<List<int>> stateSequence, bool isRedBall)
    {
        var stateFeatures = new List<string>();
        
        foreach (var balls in stateSequence)
        {
            // 使用多种特征来表示状态
            var sortedBalls = balls.OrderBy(x => x).ToArray();
            
            // 特征1：球号组合
            stateFeatures.Add(string.Join(",", sortedBalls));
            
            if (isRedBall)
            {
                // 特征2：奇偶模式
                var oddCount = balls.Count(x => x % 2 == 1);
                stateFeatures.Add($"O{oddCount}E{5 - oddCount}");
                
                // 特征3：大小模式
                var largeCount = balls.Count(x => x >= 18);
                stateFeatures.Add($"L{largeCount}S{5 - largeCount}");
                
                // 特征4：区间分布
                var zone1 = balls.Count(x => x <= 7);
                var zone2 = balls.Count(x => x >= 8 && x <= 14);
                var zone3 = balls.Count(x => x >= 15 && x <= 21);
                var zone4 = balls.Count(x => x >= 22 && x <= 28);
                var zone5 = balls.Count(x => x >= 29);
                stateFeatures.Add($"Z{zone1}{zone2}{zone3}{zone4}{zone5}");
            }
            else
            {
                // 蓝球特征
                var oddCount = balls.Count(x => x % 2 == 1);
                stateFeatures.Add($"BO{oddCount}E{2 - oddCount}");
                
                var largeCount = balls.Count(x => x >= 7);
                stateFeatures.Add($"BL{largeCount}S{2 - largeCount}");
            }
        }
        
        return string.Join("|", stateFeatures);
    }
    
    private int[] PredictBalls(List<LotteryDataRow> recentData, bool isRedBall)
    {
        var transitionMatrix = isRedBall ? _redTransitionMatrix : _blueTransitionMatrix;
        var ballCount = isRedBall ? 5 : 2;
        var maxBall = isRedBall ? 35 : 12;
        
        // 构建当前状态
        var stateSequences = recentData.Select(row => 
            isRedBall ? row.RedBalls.ToList() : row.BlueBalls.ToList()).ToList();
        var currentState = BuildStateKey(stateSequences, isRedBall);
        
        var selectedBalls = new HashSet<int>();
        var random = new Random();
        
        // 如果找到匹配的状态，使用转移概率
        if (transitionMatrix.ContainsKey(currentState))
        {
            var probabilities = transitionMatrix[currentState];
            
            // 按概率选择球号
            while (selectedBalls.Count < ballCount)
            {
                var ball = SelectBallByProbability(probabilities, selectedBalls);
                if (ball > 0)
                {
                    selectedBalls.Add(ball);
                }
                else
                {
                    // 如果概率选择失败，随机选择
                    var remainingBalls = Enumerable.Range(1, maxBall).Except(selectedBalls).ToList();
                    if (remainingBalls.Any())
                    {
                        selectedBalls.Add(remainingBalls[random.Next(remainingBalls.Count)]);
                    }
                }
            }
        }
        else
        {
            // 如果没有找到匹配状态，使用相似状态或随机选择
            var similarStates = FindSimilarStates(currentState, transitionMatrix.Keys);
            
            if (similarStates.Any())
            {
                var bestState = similarStates.First();
                var probabilities = transitionMatrix[bestState];
                
                while (selectedBalls.Count < ballCount)
                {
                    var ball = SelectBallByProbability(probabilities, selectedBalls);
                    if (ball > 0)
                    {
                        selectedBalls.Add(ball);
                    }
                    else
                    {
                        break;
                    }
                }
            }
            
            // 补充随机选择
            while (selectedBalls.Count < ballCount)
            {
                var remainingBalls = Enumerable.Range(1, maxBall).Except(selectedBalls).ToList();
                if (remainingBalls.Any())
                {
                    selectedBalls.Add(remainingBalls[random.Next(remainingBalls.Count)]);
                }
                else
                {
                    break;
                }
            }
        }
        
        return selectedBalls.OrderBy(x => x).ToArray();
    }
    
    private int SelectBallByProbability(Dictionary<int, double> probabilities, HashSet<int> excludeBalls)
    {
        var random = new Random();
        var availableProbs = probabilities.Where(p => !excludeBalls.Contains(p.Key) && p.Value > 0).ToList();
        
        if (!availableProbs.Any())
            return -1;
        
        var totalProb = availableProbs.Sum(p => p.Value);
        var randomValue = random.NextDouble() * totalProb;
        
        double cumulative = 0;
        foreach (var prob in availableProbs)
        {
            cumulative += prob.Value;
            if (randomValue <= cumulative)
            {
                return prob.Key;
            }
        }
        
        return availableProbs.Last().Key;
    }
    
    private List<string> FindSimilarStates(string targetState, IEnumerable<string> availableStates)
    {
        var similarities = new List<(string State, double Similarity)>();
        
        foreach (var state in availableStates)
        {
            var similarity = CalculateStateSimilarity(targetState, state);
            similarities.Add((state, similarity));
        }
        
        return similarities.OrderByDescending(s => s.Similarity)
                          .Take(3)
                          .Select(s => s.State)
                          .ToList();
    }
    
    private double CalculateStateSimilarity(string state1, string state2)
    {
        var parts1 = state1.Split('|');
        var parts2 = state2.Split('|');
        
        if (parts1.Length != parts2.Length)
            return 0;
        
        int matches = 0;
        for (int i = 0; i < parts1.Length; i++)
        {
            if (parts1[i] == parts2[i])
                matches++;
        }
        
        return (double)matches / parts1.Length;
    }
    
    private double CalculateConfidence(int[] redBalls, int[] blueBalls, List<LotteryDataRow> recentData)
    {
        // 基于状态匹配度的置信度计算
        var stateSequences = recentData.Select(row => row.RedBalls.ToList()).ToList();
        var currentRedState = BuildStateKey(stateSequences, true);
        
        var blueStateSequences = recentData.Select(row => row.BlueBalls.ToList()).ToList();
        var currentBlueState = BuildStateKey(blueStateSequences, false);
        
        var redConfidence = _redTransitionMatrix.ContainsKey(currentRedState) ? 0.8 : 0.4;
        var blueConfidence = _blueTransitionMatrix.ContainsKey(currentBlueState) ? 0.8 : 0.4;
        
        return (redConfidence * 0.7 + blueConfidence * 0.3) * 100;
    }
    
    private void CalculateMetrics()
    {
        var redStates = _redTransitionMatrix.Count;
        var blueStates = _blueTransitionMatrix.Count;
        
        UpdateMetrics("RedStates", redStates);
        UpdateMetrics("BlueStates", blueStates);
        UpdateMetrics("TotalStates", redStates + blueStates);
        UpdateMetrics("OverallScore", Math.Min(100, (redStates + blueStates) * 2));
        UpdateMetrics("MarkovOrder", _order);
    }
    
    public override async Task SaveModelAsync(string filePath, CancellationToken cancellationToken = default)
    {
        // 实现模型保存逻辑
        await Task.CompletedTask;
    }
    
    public override async Task LoadModelAsync(string filePath, CancellationToken cancellationToken = default)
    {
        // 实现模型加载逻辑
        await Task.CompletedTask;
    }
}