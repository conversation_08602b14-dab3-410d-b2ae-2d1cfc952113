using DLT_CP.Models;
using DLT_CP.Exceptions;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace DLT_CP.Services;

/// <summary>
/// 配置验证服务
/// </summary>
public class ConfigurationValidator
{
    private readonly ILogger<ConfigurationValidator> _logger;
    private readonly GlobalExceptionHandler? _exceptionHandler;

    public ConfigurationValidator(ILogger<ConfigurationValidator> logger, GlobalExceptionHandler? exceptionHandler = null)
    {
        _logger = logger;
        _exceptionHandler = exceptionHandler;
    }

    /// <summary>
    /// 验证彩票配置
    /// </summary>
    public ValidationResult ValidateLotteryConfig(LotteryConfig config)
    {
        var result = new ValidationResult();
        
        try
        {
            _logger.LogDebug("开始验证彩票配置");
            
            if (config == null)
            {
                result.AddError("配置对象为空");
                return result;
            }

            // 验证数据路径
            ValidateDataPath(config.DataPath, result);
            
            // 验证序列长度
            ValidateSequenceLength(config.SequenceLength, result);
            
            // 验证回测周期
            ValidateBacktestPeriods(config.BacktestPeriods, result);
            
            _logger.LogDebug("彩票配置验证完成 - 错误数: {ErrorCount}", result.Errors.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            var validationEx = new DataProcessingException(
                $"配置验证过程中发生错误: {ex.Message}", 
                "配置验证", 
                ex);
            
            _exceptionHandler?.HandleBusinessException(validationEx, "配置验证服务");
            result.AddError($"验证过程异常: {ex.Message}");
            return result;
        }
    }

    /// <summary>
    /// 验证模型配置
    /// </summary>
    public ValidationResult ValidateModelConfigs(ModelConfigs config)
    {
        var result = new ValidationResult();
        
        try
        {
            _logger.LogDebug("开始验证模型配置");
            
            if (config == null)
            {
                result.AddError("模型配置对象为空");
                return result;
            }

            // 验证马尔可夫配置
            ValidateMarkovConfig(config, result);
            
            // 验证神经网络配置
            ValidateNeuralNetworkConfig(config, result);
            
            _logger.LogDebug("模型配置验证完成 - 错误数: {ErrorCount}", result.Errors.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            var validationEx = new DataProcessingException(
                $"模型配置验证过程中发生错误: {ex.Message}", 
                "模型配置验证", 
                ex);
            
            _exceptionHandler?.HandleBusinessException(validationEx, "配置验证服务");
            result.AddError($"验证过程异常: {ex.Message}");
            return result;
        }
    }

    /// <summary>
    /// 验证系统环境
    /// </summary>
    public ValidationResult ValidateSystemEnvironment()
    {
        var result = new ValidationResult();
        
        try
        {
            _logger.LogDebug("开始验证系统环境");
            
            // 检查内存
            ValidateMemory(result);
            
            // 检查磁盘空间
            ValidateDiskSpace(result);
            
            // 检查.NET版本
            ValidateDotNetVersion(result);
            
            _logger.LogDebug("系统环境验证完成 - 错误数: {ErrorCount}", result.Errors.Count);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "系统环境验证过程中发生错误");
            result.AddError($"环境验证异常: {ex.Message}");
            return result;
        }
    }

    private void ValidateDataPath(string dataPath, ValidationResult result)
    {
        if (string.IsNullOrWhiteSpace(dataPath))
        {
            result.AddError("数据路径不能为空");
            return;
        }

        var directory = Path.GetDirectoryName(dataPath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            result.AddWarning($"数据目录不存在: {directory}");
        }

        var extension = Path.GetExtension(dataPath).ToLowerInvariant();
        if (extension != ".csv" && extension != ".json")
        {
            result.AddError($"不支持的数据文件格式: {extension}");
        }

        if (File.Exists(dataPath))
        {
            var fileInfo = new FileInfo(dataPath);
            if (fileInfo.Length == 0)
            {
                result.AddWarning("数据文件为空");
            }
            else if (fileInfo.Length > 100 * 1024 * 1024) // 100MB
            {
                result.AddWarning($"数据文件较大: {fileInfo.Length / (1024 * 1024)}MB，可能影响加载性能");
            }
        }
        else
        {
            result.AddWarning($"数据文件不存在: {dataPath}");
        }
    }

    private void ValidateSequenceLength(int sequenceLength, ValidationResult result)
    {
        if (sequenceLength <= 0)
        {
            result.AddError("序列长度必须大于0");
        }
        else if (sequenceLength < 10)
        {
            result.AddWarning("序列长度较短，可能影响预测准确性");
        }
        else if (sequenceLength > 1000)
        {
            result.AddWarning("序列长度较长，可能影响训练性能");
        }
    }

    private void ValidateBacktestPeriods(int backtestPeriods, ValidationResult result)
    {
        if (backtestPeriods <= 0)
        {
            result.AddError("回测周期必须大于0");
        }
        else if (backtestPeriods < 10)
        {
            result.AddWarning("回测周期较短，可能无法充分验证模型性能");
        }
        else if (backtestPeriods > 500)
        {
            result.AddWarning("回测周期较长，可能影响回测性能");
        }
    }

    private void ValidateMarkovConfig(ModelConfigs config, ValidationResult result)
    {
        if (config.MarkovOrder <= 0)
        {
            result.AddError("马尔可夫阶数必须大于0");
        }
        else if (config.MarkovOrder > 10)
        {
            result.AddWarning("马尔可夫阶数较高，可能导致状态空间过大");
        }
    }

    private void ValidateNeuralNetworkConfig(ModelConfigs config, ValidationResult result)
    {
        if (config.Epochs <= 0)
        {
            result.AddError("训练轮数必须大于0");
        }
        else if (config.Epochs > 1000)
        {
            result.AddWarning("训练轮数较多，可能导致过拟合或训练时间过长");
        }

        if (config.BatchSize <= 0)
        {
            result.AddError("批次大小必须大于0");
        }
        else if (config.BatchSize > 1024)
        {
            result.AddWarning("批次大小较大，可能导致内存不足");
        }

        if (config.LearningRate <= 0 || config.LearningRate >= 1)
        {
            result.AddError("学习率必须在0到1之间");
        }
        else if (config.LearningRate > 0.1)
        {
            result.AddWarning("学习率较高，可能导致训练不稳定");
        }
        else if (config.LearningRate < 0.0001)
        {
            result.AddWarning("学习率较低，可能导致训练缓慢");
        }

        if (config.HiddenSize <= 0)
        {
            result.AddError("隐藏层大小必须大于0");
        }
        else if (config.HiddenSize > 1024)
        {
            result.AddWarning("隐藏层较大，可能导致内存不足或过拟合");
        }
    }

    private void ValidateMemory(ValidationResult result)
    {
        try
        {
            var totalMemory = GC.GetTotalMemory(false);
            var availableMemory = Environment.WorkingSet;
            
            _logger.LogDebug("内存使用情况 - 总内存: {TotalMemory}MB, 工作集: {WorkingSet}MB", 
                totalMemory / (1024 * 1024), availableMemory / (1024 * 1024));
            
            if (availableMemory < 512 * 1024 * 1024) // 512MB
            {
                result.AddWarning("可用内存较少，可能影响大数据集处理性能");
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "无法获取内存信息");
            result.AddWarning("无法检查内存状态");
        }
    }

    private void ValidateDiskSpace(ValidationResult result)
    {
        try
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            var drive = new DriveInfo(Path.GetPathRoot(currentDirectory) ?? "C:\\");
            
            if (drive.IsReady)
            {
                var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
                
                _logger.LogDebug("磁盘空间 - 可用空间: {FreeSpace}GB", freeSpaceGB);
                
                if (freeSpaceGB < 1)
                {
                    result.AddWarning("磁盘可用空间不足1GB，可能影响日志和模型文件存储");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "无法获取磁盘空间信息");
            result.AddWarning("无法检查磁盘空间");
        }
    }

    private void ValidateDotNetVersion(ValidationResult result)
    {
        try
        {
            var version = Environment.Version;
            _logger.LogDebug(".NET版本: {Version}", version);
            
            if (version.Major < 6)
            {
                result.AddWarning($"当前.NET版本({version})较旧，建议升级到.NET 6或更高版本");
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "无法获取.NET版本信息");
            result.AddWarning("无法检查.NET版本");
        }
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public List<string> Errors { get; } = new();
    public List<string> Warnings { get; } = new();
    
    public bool IsValid => Errors.Count == 0;
    public bool HasWarnings => Warnings.Count > 0;
    
    public void AddError(string error)
    {
        Errors.Add(error);
    }
    
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
    
    public string GetSummary()
    {
        var summary = new List<string>();
        
        if (Errors.Count > 0)
        {
            summary.Add($"错误 ({Errors.Count}):");
            summary.AddRange(Errors.Select(e => $"  - {e}"));
        }
        
        if (Warnings.Count > 0)
        {
            summary.Add($"警告 ({Warnings.Count}):");
            summary.AddRange(Warnings.Select(w => $"  - {w}"));
        }
        
        return summary.Count > 0 ? string.Join(Environment.NewLine, summary) : "验证通过";
    }
}