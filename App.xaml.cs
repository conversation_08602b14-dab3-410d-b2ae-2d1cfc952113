using System.Configuration;
using System.Data;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using DLT_CP.Services;
using DLT_CP.ViewModels;
using DLT_CP.Models;
using DLT_CP.Exceptions;

namespace DLT_CP
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 创建主机构建器
            _host = Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册配置
                    services.Configure<LotteryConfig>(context.Configuration.GetSection("LotteryConfig"));
                    services.Configure<ModelConfigs>(context.Configuration.GetSection("ModelConfigs"));

                    // 注册核心服务
                    services.AddSingleton<GlobalExceptionHandler>();
                    services.AddSingleton<ConfigurationValidator>();
                    services.AddSingleton<MemoryManager>();
                    services.AddSingleton<PerformanceMonitor>();
                    services.AddSingleton<CacheService>();
                    services.AddSingleton<IDataService, DataService>();
                    services.AddSingleton<IBacktestFramework, BacktestFramework>();
                    
                    // 注册预测器
                    services.AddTransient<FrequencyAnalyzer>();
                    services.AddTransient<MarkovChainPredictor>();
                    services.AddTransient<MLNetPredictor>();
                    services.AddTransient<TorchSharpPredictor>();
                    
                    // 注册ViewModels
                    services.AddTransient<MainViewModel>();
                    services.AddTransient<PerformanceMonitorViewModel>();
                    
                    // 注册主窗口
                    services.AddTransient<MainWindow>();
                })
                .ConfigureLogging(logging =>
                {
                    logging.AddConsole();
                    logging.AddDebug();
                })
                .Build();

            await _host.StartAsync();

            // 获取主窗口并显示
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            // 设置全局异常处理
            SetupGlobalExceptionHandling();
            
            base.OnStartup(e);
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
            base.OnExit(e);
        }
        
        private void SetupGlobalExceptionHandling()
        {
            // 处理UI线程未捕获异常
            DispatcherUnhandledException += (sender, e) =>
            {
                var handler = _host?.Services.GetService<GlobalExceptionHandler>();
                handler?.HandleUnhandledException(e.Exception, false);
                e.Handled = true; // 防止应用程序崩溃
            };
            
            // 处理非UI线程未捕获异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var handler = _host?.Services.GetService<GlobalExceptionHandler>();
                handler?.HandleUnhandledException((Exception)e.ExceptionObject, e.IsTerminating);
            };
            
            // 处理Task未捕获异常
            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                var handler = _host?.Services.GetService<GlobalExceptionHandler>();
                handler?.HandleUnhandledException(e.Exception, false);
                e.SetObserved(); // 防止应用程序崩溃
            };
        }
    }
}
