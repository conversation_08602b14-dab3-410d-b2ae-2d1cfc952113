<Window x:Class="DLT_CP.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DLT_CP"
        xmlns:views="clr-namespace:DLT_CP.Views"
        xmlns:converters="clr-namespace:DLT_CP.Converters"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="大乐透预测系统 - DLT_CP" Height="900" Width="1400" 
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        
        <!-- 样式定义 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDC3C7"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#95A5A6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#7F8C8D"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="NumberBallStyle" TargetType="Border">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="CornerRadius" Value="17.5"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>
        
        <Style x:Key="RedBallStyle" TargetType="Border" BasedOn="{StaticResource NumberBallStyle}">
            <Setter Property="Background" Value="#E74C3C"/>
            <Setter Property="BorderBrush" Value="#C0392B"/>
        </Style>
        
        <Style x:Key="BlueBallStyle" TargetType="Border" BasedOn="{StaticResource NumberBallStyle}">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="BorderBrush" Value="#2980B9"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#34495E" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🎯" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBlock Text="大乐透智能预测系统" FontSize="24" FontWeight="Bold" 
                              Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="v1.0" FontSize="12" Foreground="#BDC3C7" 
                              VerticalAlignment="Bottom" Margin="10,0,0,5"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="当前时间:" Foreground="#BDC3C7" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}"
                              Foreground="White" Margin="5,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧控制面板 -->
            <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,10,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 预测器选择 -->
                        <TextBlock Text="预测器选择" Style="{StaticResource HeaderTextStyle}"/>
                        <ComboBox ItemsSource="{Binding AvailablePredictors}" 
                                 SelectedItem="{Binding SelectedPredictor}"
                                 Margin="0,0,0,10" Padding="10,8" FontSize="14"/>
                        
                        <TextBlock Text="{Binding PredictorDescription}" 
                                  TextWrapping="Wrap" Foreground="#7F8C8D" 
                                  FontSize="12" Margin="0,0,0,20"/>
                        
                        <!-- 期号输入 -->
                        <TextBlock Text="预测期号" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBox Text="{Binding NextPeriodNumber, UpdateSourceTrigger=PropertyChanged}" 
                                Padding="10,8" FontSize="14" Margin="0,0,0,20"/>
                        
                        <!-- 操作按钮 -->
                        <TextBlock Text="操作" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <Button Content="加载历史数据" 
                               Command="{Binding LoadDataCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"/>
                        
                        <Button Content="训练模型" 
                               Command="{Binding TrainModelCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"/>
                        
                        <Button Content="开始预测" 
                               Command="{Binding PredictCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"/>
                        
                        <Button Content="执行回测" 
                               Command="{Binding RunBacktestCommand}"
                               Style="{StaticResource PrimaryButtonStyle}"/>
                        
                        <Separator Margin="0,20"/>
                        
                        <Button Content="导出数据" 
                               Command="{Binding ExportDataCommand}"
                               Style="{StaticResource SecondaryButtonStyle}"/>
                        
                        <Button Content="清空历史" 
                               Command="{Binding ClearHistoryCommand}"
                               Style="{StaticResource SecondaryButtonStyle}"/>
                        
                        <!-- 进度显示 -->
                        <StackPanel Margin="0,20,0,0" 
                                   Visibility="{Binding IsTraining, Converter={StaticResource BoolToVisibilityConverter}}">
                            <TextBlock Text="训练进度" Style="{StaticResource HeaderTextStyle}"/>
                            <ProgressBar Value="{Binding ProgressValue}" Height="20" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding StatusMessage}" FontSize="12" Foreground="#7F8C8D"/>
                        </StackPanel>
                        
                        <StackPanel Margin="0,20,0,0" 
                                   Visibility="{Binding IsPredicting, Converter={StaticResource BoolToVisibilityConverter}}">
                            <TextBlock Text="预测进度" Style="{StaticResource HeaderTextStyle}"/>
                            <ProgressBar IsIndeterminate="True" Height="20" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding StatusMessage}" FontSize="12" Foreground="#7F8C8D"/>
                        </StackPanel>
                        
                        <StackPanel Margin="0,20,0,0" 
                                   Visibility="{Binding IsBacktesting, Converter={StaticResource BoolToVisibilityConverter}}">
                            <TextBlock Text="回测进度" Style="{StaticResource HeaderTextStyle}"/>
                            <ProgressBar Value="{Binding ProgressValue}" Height="20" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding StatusMessage}" FontSize="12" Foreground="#7F8C8D"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- 右侧主显示区域 -->
            <TabControl Grid.Column="1" Margin="10,0,0,0">
                <!-- 预测结果标签页 -->
                <TabItem Header="预测结果">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 当前预测结果 -->
                        <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                               Visibility="{Binding CurrentPrediction, Converter={StaticResource BoolToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock Text="最新预测结果" Style="{StaticResource HeaderTextStyle}"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" Margin="0,0,30,0">
                                        <TextBlock Text="期号:" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding CurrentPrediction.PeriodNumber}" FontSize="18" Foreground="#E74C3C"/>
                                        
                                        <TextBlock Text="置信度:" FontWeight="Bold" Margin="0,10,0,5"/>
                                        <TextBlock Text="{Binding CurrentPrediction.Confidence, StringFormat='{}{0:F1}%'}" 
                                                  FontSize="16" Foreground="#27AE60"/>
                                        
                                        <TextBlock Text="预测时间:" FontWeight="Bold" Margin="0,10,0,5"/>
                                        <TextBlock Text="{Binding CurrentPrediction.PredictionTime, StringFormat='{}{0:MM-dd HH:mm}'}" 
                                                  FontSize="12" Foreground="#7F8C8D"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="预测号码" FontWeight="Bold" Margin="0,0,0,10"/>
                                        
                                        <!-- 红球显示 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                            <TextBlock Text="红球:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                                            <Border Style="{StaticResource RedBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Red1}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Style="{StaticResource RedBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Red2}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Style="{StaticResource RedBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Red3}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Style="{StaticResource RedBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Red4}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Style="{StaticResource RedBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Red5}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                        </StackPanel>
                                        
                                        <!-- 蓝球显示 -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="蓝球:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                                            <Border Style="{StaticResource BlueBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Blue1}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                            <Border Style="{StaticResource BlueBallStyle}">
                                                <TextBlock Text="{Binding CurrentPrediction.Blue2}" 
                                                          HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                          Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                        </StackPanel>
                                        
                                        <TextBlock Text="{Binding CurrentPrediction.Notes}" 
                                                  TextWrapping="Wrap" FontSize="12" 
                                                  Foreground="#7F8C8D" Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>
                        
                        <!-- 预测历史 -->
                        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="预测历史" Style="{StaticResource HeaderTextStyle}"/>
                                
                                <DataGrid Grid.Row="1" ItemsSource="{Binding PredictionHistory}" 
                                         AutoGenerateColumns="False" IsReadOnly="True" 
                                         GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                         AlternatingRowBackground="#F8F9FA">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="期号" Binding="{Binding PeriodNumber}" Width="80"/>
                                        <DataGridTextColumn Header="预测时间" Binding="{Binding PredictionTime, StringFormat='{}{0:MM-dd HH:mm}'}" Width="100"/>
                                        <DataGridTextColumn Header="红球" Binding="{Binding RedBallsDisplay}" Width="150"/>
                                        <DataGridTextColumn Header="蓝球" Binding="{Binding BlueBallsDisplay}" Width="80"/>
                                        <DataGridTextColumn Header="置信度" Binding="{Binding Confidence, StringFormat='{}{0:F1}%'}" Width="80"/>
                                        <DataGridTextColumn Header="模型" Binding="{Binding ModelName}" Width="120"/>
                                        <DataGridTextColumn Header="评分" Binding="{Binding ModelScore, StringFormat='{}{0:F1}'}" Width="60"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </Grid>
                </TabItem>
                
                <!-- 历史数据标签页 -->
                <TabItem Header="历史数据">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="历史开奖数据" Style="{StaticResource HeaderTextStyle}" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding HistoryData.Count, StringFormat={}共 {0} 期}" 
                                      FontSize="14" Foreground="#7F8C8D" VerticalAlignment="Center" Margin="20,0,0,0"/>
                        </StackPanel>
                        
                        <DataGrid Grid.Row="1" ItemsSource="{Binding HistoricalData}" 
                                 AutoGenerateColumns="False" IsReadOnly="True" 
                                 GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                 AlternatingRowBackground="#F8F9FA">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="期号" Binding="{Binding PeriodNumber}" Width="80"/>
                                <DataGridTextColumn Header="开奖日期" Binding="{Binding DrawDate, StringFormat='{}{0:yyyy-MM-dd}'}" Width="100"/>
                                <DataGridTextColumn Header="红球" Binding="{Binding RedBallsDisplay}" Width="150"/>
                                <DataGridTextColumn Header="蓝球" Binding="{Binding BlueBallsDisplay}" Width="80"/>
                                <DataGridTextColumn Header="红球和值" Binding="{Binding RedBallSum}" Width="80"/>
                                <DataGridTextColumn Header="蓝球和值" Binding="{Binding BlueBallSum}" Width="80"/>
                                <DataGridTextColumn Header="奇偶比" Binding="{Binding OddEvenRatioDisplay}" Width="80"/>
                                <DataGridTextColumn Header="大小比" Binding="{Binding HighLowRatioDisplay}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>
                
                <!-- 回测结果标签页 -->
                <TabItem Header="回测分析">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 当前回测结果 -->
                        <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                               Visibility="{Binding CurrentBacktestResult, Converter={StaticResource BoolToVisibilityConverter}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="模型信息" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.ModelName}" FontSize="16" Foreground="#E74C3C" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.TestPeriods, StringFormat='测试期数: {0}'}" FontSize="12" Foreground="#7F8C8D" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.TestTime, StringFormat='测试时间: {0:MM-dd HH:mm}'}" FontSize="12" Foreground="#7F8C8D"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="命中统计" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.AverageRedHits, StringFormat='平均红球命中: {0:F1}'}" FontSize="14" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.AverageBlueHits, StringFormat='平均蓝球命中: {0:F1}'}" FontSize="14" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.RedHitRate, StringFormat='红球命中率: {0:F1}%'}" FontSize="12" Foreground="#7F8C8D" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.BlueHitRate, StringFormat='蓝球命中率: {0:F1}%'}" FontSize="12" Foreground="#7F8C8D"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="综合评价" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.OverallScore, StringFormat='综合评分: {0:F1}'}" FontSize="16" Foreground="#27AE60" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.AccuracyLevel}" FontSize="14" Foreground="#E67E22" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentBacktestResult.BestRecord}" FontSize="12" Foreground="#7F8C8D" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                        
                        <!-- 回测历史 -->
                        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Text="回测历史" Style="{StaticResource HeaderTextStyle}"/>
                                
                                <DataGrid Grid.Row="1" ItemsSource="{Binding BacktestResults}" 
                                         AutoGenerateColumns="False" IsReadOnly="True" 
                                         GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                         AlternatingRowBackground="#F8F9FA">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="模型" Binding="{Binding ModelName}" Width="120"/>
                                        <DataGridTextColumn Header="测试时间" Binding="{Binding TestTime, StringFormat='{}{0:MM-dd HH:mm}'}" Width="100"/>
                                        <DataGridTextColumn Header="测试期数" Binding="{Binding TestPeriods}" Width="80"/>
                                        <DataGridTextColumn Header="平均红球命中" Binding="{Binding AverageRedHits, StringFormat='{}{0:F1}'}" Width="100"/>
                                        <DataGridTextColumn Header="平均蓝球命中" Binding="{Binding AverageBlueHits, StringFormat='{}{0:F1}'}" Width="100"/>
                                        <DataGridTextColumn Header="综合评分" Binding="{Binding OverallScore, StringFormat='{}{0:F1}'}" Width="80"/>
                                        <DataGridTextColumn Header="准确度等级" Binding="{Binding AccuracyLevel}" Width="100"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </Grid>
                </TabItem>
                
                <!-- 性能监控标签页 -->
                <TabItem Header="性能监控">
                    <views:PerformanceMonitorView/>
                </TabItem>
            </TabControl>
        </Grid>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#ECF0F1" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" 
                          VerticalAlignment="Center" Foreground="#2C3E50"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="预测器:" Foreground="#7F8C8D" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding SelectedPredictor}" Foreground="#2C3E50" 
                              Margin="5,0,20,0" VerticalAlignment="Center" FontWeight="Bold"/>
                    
                    <TextBlock Text="数据量:" Foreground="#7F8C8D" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding HistoryData.Count, StringFormat={}{0} 期}" 
                              Foreground="#2C3E50" Margin="5,0,0,0" VerticalAlignment="Center" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
